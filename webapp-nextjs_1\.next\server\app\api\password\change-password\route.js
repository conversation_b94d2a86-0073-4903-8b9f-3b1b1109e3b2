(()=>{var e={};e.id=802,e.ids=[802],e.modules={3201:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>l,routeModule:()=>u,serverHooks:()=>h,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>c});var s={};t.r(s),t.d(s,{POST:()=>p});var a=t(96559),o=t(48088),n=t(37719),i=t(32190);async function p(e){try{let r=await e.json(),t=e.headers.get("authorization");if(!t||!t.startsWith("Bearer "))return i.NextResponse.json({success:!1,detail:"Token di autorizzazione mancante"},{status:401});let s=await fetch("http://localhost:8001/api/password/change-password",{method:"POST",headers:{"Content-Type":"application/json",Authorization:t},body:JSON.stringify(r)}),a=await s.json();return i.NextResponse.json(a,{status:s.status,headers:{"Content-Type":"application/json"}})}catch(e){return console.error("Password change error:",e),i.NextResponse.json({success:!1,detail:"Errore interno del server"},{status:500})}}let u=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/password/change-password/route",pathname:"/api/password/change-password",filename:"route",bundlePath:"app/api/password/change-password/route"},resolvedPagePath:"C:\\CMS\\webapp-nextjs\\src\\app\\api\\password\\change-password\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:c,serverHooks:h}=u;function l(){return(0,n.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:c})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,580],()=>t(3201));module.exports=s})();