"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7697],{381:(a,t,e)=>{e.d(t,{A:()=>c});let c=(0,e(19946).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},25731:(a,t,e)=>{e.d(t,{AR:()=>s,At:()=>n,CV:()=>p,Fw:()=>r,ZQ:()=>o,_I:()=>b,dG:()=>h,km:()=>l,kw:()=>d,l9:()=>g,mg:()=>m,om:()=>u,ug:()=>v});let c=e(23464).A.create({baseURL:"http://localhost:8001",timeout:3e4,headers:{"Content-Type":"application/json"}});c.interceptors.request.use(a=>{{let t=localStorage.getItem("token");t&&(a.headers.Authorization="Bearer ".concat(t))}return a},a=>Promise.reject(a)),c.interceptors.response.use(a=>a,a=>{var t;return(null==(t=a.response)?void 0:t.status)===401&&(localStorage.removeItem("token"),localStorage.removeItem("access_token"),localStorage.removeItem("user_data"),localStorage.removeItem("cantiere_data"),window.location.href="/login"),Promise.reject(a)});let i={get:async(a,t)=>(await c.get(a,t)).data,post:async(a,t,e)=>(await c.post(a,t,e)).data,put:async(a,t,e)=>(await c.put(a,t,e)).data,delete:async(a,t)=>(await c.delete(a,t)).data},o={login:async a=>{let t=new FormData;return t.append("username",a.username),t.append("password",a.password),(await c.post("/api/auth/login",t,{headers:{"Content-Type":"application/x-www-form-urlencoded"}})).data},loginCantiere:a=>i.post("/api/auth/login/cantiere",{codice_univoco:a.codice_cantiere,password:a.password_cantiere}),verifyToken:()=>i.post("/api/auth/test-token"),logout:()=>{localStorage.removeItem("access_token"),localStorage.removeItem("user_data"),window.location.href="/login"}},n={getCavi:(a,t)=>i.get("/api/cavi/".concat(a),{params:t}),getCavo:(a,t)=>i.get("/api/cavi/".concat(a,"/").concat(t)),checkCavo:(a,t)=>i.get("/api/cavi/".concat(a,"/check/").concat(t)),createCavo:(a,t)=>i.post("/api/cavi/".concat(a),t),updateCavo:(a,t,e)=>i.put("/api/cavi/".concat(a,"/").concat(t),e),deleteCavo:(a,t,e)=>i.delete("/api/cavi/".concat(a,"/").concat(t),{data:e}),updateMetriPosati:(a,t,e,c,o)=>i.post("/api/cavi/".concat(a,"/").concat(t,"/metri-posati"),{metri_posati:e,id_bobina:c,force_over:o||!1}),updateBobina:(a,t,e,c)=>i.post("/api/cavi/".concat(a,"/").concat(t,"/bobina"),{id_bobina:e,force_over:c||!1}),cancelInstallation:(a,t)=>i.post("/api/cavi/".concat(a,"/").concat(t,"/cancel-installation")),collegaCavo:(a,t,e,c)=>i.post("/api/cavi/".concat(a,"/").concat(t,"/collegamento"),{lato:e,responsabile:c}),scollegaCavo:(a,t,e)=>{let c={};return e&&(c.data={lato:e}),i.delete("/api/cavi/".concat(a,"/").concat(t,"/collegamento"),c)},markAsSpare:function(a,t,e){let c=!(arguments.length>3)||void 0===arguments[3]||arguments[3];return e?i.post("/api/cavi/".concat(a,"/").concat(t,"/mark-as-spare"),{force:c}):i.post("/api/cavi/".concat(a,"/").concat(t,"/reactivate-spare"),{})},debugCavi:a=>i.get("/api/cavi/debug/".concat(a)),debugCaviRaw:a=>i.get("/api/cavi/debug/raw/".concat(a))},r={getBobine:(a,t)=>i.get("/api/parco-cavi/".concat(a),{params:t}),getBobina:(a,t)=>i.get("/api/parco-cavi/".concat(a,"/").concat(t)),getBobineCompatibili:(a,t)=>i.get("/api/parco-cavi/".concat(a,"/compatibili"),{params:t}),createBobina:(a,t)=>i.post("/api/parco-cavi/".concat(a),t),updateBobina:(a,t,e)=>i.put("/api/parco-cavi/".concat(a,"/").concat(t),e),deleteBobina:(a,t)=>i.delete("/api/parco-cavi/".concat(a,"/").concat(t)),isFirstBobinaInsertion:a=>i.get("/api/parco-cavi/".concat(a,"/is-first-insertion")),updateBobina:(a,t,e)=>i.put("/api/parco-cavi/".concat(a,"/").concat(t),e),deleteBobina:(a,t)=>i.delete("/api/parco-cavi/".concat(a,"/").concat(t)),checkDisponibilita:(a,t,e)=>i.get("/api/parco-cavi/".concat(a,"/").concat(t,"/disponibilita"),{params:{metri_richiesti:e}})},p={getComande:a=>i.get("/api/comande/cantiere/".concat(a)),getComanda:(a,t)=>i.get("/api/comande/".concat(t)),getCaviComanda:a=>i.get("/api/comande/".concat(a,"/cavi")),createComanda:(a,t)=>i.post("/api/comande/cantiere/".concat(a),t),createComandaWithCavi:(a,t,e)=>i.post("/api/comande/cantiere/".concat(a,"/crea-con-cavi"),t,{params:{lista_id_cavi:e}}),updateDatiComanda:(a,t,e)=>i.put("/api/comande/".concat(a,"/").concat(t),e),updateComanda:(a,t,e)=>i.put("/api/comande/cantiere/".concat(a,"/").concat(t),e),deleteComanda:(a,t)=>i.delete("/api/comande/cantiere/".concat(a,"/").concat(t)),assegnaCavi:(a,t,e)=>i.post("/api/comande/cantiere/".concat(a,"/").concat(t,"/assegna-cavi"),{cavi_ids:e}),rimuoviCavi:(a,t,e)=>i.delete("/api/comande/cantiere/".concat(a,"/").concat(t,"/rimuovi-cavi"),{data:{cavi_ids:e}}),getStatistiche:a=>i.get("/api/comande/cantiere/".concat(a,"/statistiche")),cambiaStato:(a,t,e)=>i.put("/api/comande/cantiere/".concat(a,"/").concat(t,"/stato"),{nuovo_stato:e})},s={getResponsabili:a=>i.get("/api/responsabili/cantiere/".concat(a)),createResponsabile:(a,t)=>i.post("/api/responsabili/".concat(a),t),updateResponsabile:(a,t,e)=>i.put("/api/responsabili/".concat(a,"/").concat(t),e),deleteResponsabile:(a,t)=>i.delete("/api/responsabili/".concat(a,"/").concat(t))},l={getCertificazioni:(a,t)=>i.get("/api/cantieri/".concat(a,"/certificazioni"),{params:t?{filtro_cavo:t}:{}}),createCertificazione:(a,t)=>i.post("/api/cantieri/".concat(a,"/certificazioni"),t),getCertificazione:(a,t)=>i.get("/api/cantieri/".concat(a,"/certificazioni/").concat(t)),updateCertificazione:(a,t,e)=>i.put("/api/cantieri/".concat(a,"/certificazioni/").concat(t),e),deleteCertificazione:(a,t)=>i.delete("/api/cantieri/".concat(a,"/certificazioni/").concat(t)),generatePDF:(a,t)=>i.get("/api/cantieri/".concat(a,"/certificazioni/").concat(t,"/pdf"),{responseType:"blob"}),getStatistiche:a=>i.get("/api/cantieri/".concat(a,"/certificazioni/statistiche")),exportCertificazioni:(a,t)=>i.get("/api/cantieri/".concat(a,"/certificazioni/export"),{params:t,responseType:"blob"}),generateReport:function(a){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"completo";return i.get("/api/cantieri/".concat(a,"/certificazioni/report/").concat(t))},bulkDelete:(a,t)=>i.post("/api/cantieri/".concat(a,"/certificazioni/bulk-delete"),{ids:t}),generateBulkPdf:(a,t)=>i.post("/api/cantieri/".concat(a,"/certificazioni/bulk-pdf"),{ids:t},{responseType:"blob"}),validateCertificazione:(a,t)=>i.post("/api/cantieri/".concat(a,"/certificazioni/validate"),t)},d={getStrumenti:a=>i.get("/api/cantieri/".concat(a,"/strumenti")),createStrumento:(a,t)=>i.post("/api/cantieri/".concat(a,"/strumenti"),t),updateStrumento:(a,t,e)=>i.put("/api/cantieri/".concat(a,"/strumenti/").concat(t),e),deleteStrumento:(a,t)=>i.delete("/api/cantieri/".concat(a,"/strumenti/").concat(t))},g={getRapporti:function(a){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:100;return i.get("/api/cantieri/".concat(a,"/rapporti"),{params:{skip:t,limit:e}})},createRapporto:(a,t)=>i.post("/api/cantieri/".concat(a,"/rapporti"),t),getRapporto:(a,t)=>i.get("/api/cantieri/".concat(a,"/rapporti/").concat(t)),updateRapporto:(a,t,e)=>i.put("/api/cantieri/".concat(a,"/rapporti/").concat(t),e),deleteRapporto:(a,t)=>i.delete("/api/cantieri/".concat(a,"/rapporti/").concat(t)),aggiornaStatistiche:(a,t)=>i.post("/api/cantieri/".concat(a,"/rapporti/").concat(t,"/aggiorna-statistiche"))},u={getNonConformita:a=>i.get("/api/cantieri/".concat(a,"/non-conformita")),createNonConformita:(a,t)=>i.post("/api/cantieri/".concat(a,"/non-conformita"),t),updateNonConformita:(a,t,e)=>i.put("/api/cantieri/".concat(a,"/non-conformita/").concat(t),e),deleteNonConformita:(a,t)=>i.delete("/api/cantieri/".concat(a,"/non-conformita/").concat(t))},m={importCavi:(a,t,e)=>{let c=new FormData;return c.append("file",t),c.append("revisione",e),i.post("/api/excel/".concat(a,"/import-cavi"),c,{headers:{"Content-Type":"multipart/form-data"}})},importBobine:(a,t)=>{let e=new FormData;return e.append("file",t),i.post("/api/excel/".concat(a,"/import-parco-bobine"),e,{headers:{"Content-Type":"multipart/form-data"}})},exportCavi:a=>i.get("/api/excel/".concat(a,"/export-cavi"),{responseType:"blob"}),exportBobine:a=>i.get("/api/excel/".concat(a,"/export-parco-bobine"),{responseType:"blob"})},v={getReportAvanzamento:a=>i.get("/api/reports/".concat(a,"/avanzamento")),getReportBOQ:a=>i.get("/api/reports/".concat(a,"/boq")),getReportUtilizzoBobine:a=>i.get("/api/reports/".concat(a,"/storico-bobine")),getReportProgress:a=>i.get("/api/reports/".concat(a,"/progress")),getReportPosaPeriodo:(a,t,e)=>{let c=new URLSearchParams;t&&c.append("data_inizio",t),e&&c.append("data_fine",e);let o=c.toString();return i.get("/api/reports/".concat(a,"/posa-periodo").concat(o?"?".concat(o):""))}},b={getCantieri:()=>i.get("/api/cantieri"),getCantiere:a=>i.get("/api/cantieri/".concat(a)),createCantiere:a=>i.post("/api/cantieri",a),updateCantiere:(a,t)=>i.put("/api/cantieri/".concat(a),t),getCantiereStatistics:a=>i.get("/api/cantieri/".concat(a,"/statistics")),getWeatherData:a=>i.get("/api/cantieri/".concat(a,"/weather"))},h={getUsers:()=>i.get("/api/users"),getUser:a=>i.get("/api/users/".concat(a)),createUser:a=>i.post("/api/users",a),updateUser:(a,t)=>i.put("/api/users/".concat(a),t),deleteUser:a=>i.delete("/api/users/".concat(a)),toggleUserStatus:a=>i.get("/api/users/toggle/".concat(a)),checkExpiredUsers:()=>i.get("/api/users/check-expired"),impersonateUser:a=>i.post("/api/auth/impersonate",{user_id:a}),getDatabaseData:()=>i.get("/api/users/db-raw"),resetDatabase:()=>i.post("/api/admin/reset-database")}},38164:(a,t,e)=>{e.d(t,{A:()=>c});let c=(0,e(19946).A)("link",[["path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",key:"1cjeqo"}],["path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",key:"19qd67"}]])},50589:(a,t,e)=>{e.d(t,{A:()=>c});let c=(0,e(19946).A)("cloud",[["path",{d:"M17.5 19H9a7 7 0 1 1 6.71-9h1.79a4.5 4.5 0 1 1 0 9Z",key:"p7xjir"}]])},57434:(a,t,e)=>{e.d(t,{A:()=>c});let c=(0,e(19946).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},75021:(a,t,e)=>{e.d(t,{A:()=>c});let c=(0,e(19946).A)("test-tube",[["path",{d:"M14.5 2v17.5c0 1.4-1.1 2.5-2.5 2.5c-1.4 0-2.5-1.1-2.5-2.5V2",key:"125lnx"}],["path",{d:"M8.5 2h7",key:"csnxdl"}],["path",{d:"M14.5 16h-5",key:"1ox875"}]])}}]);