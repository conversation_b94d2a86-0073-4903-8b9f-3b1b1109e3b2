{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/app/api/cantieri/%5BcantiereId%5D/strumenti/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\n\ninterface StrumentoCertificato {\n  id_strumento: number\n  id_cantiere: number\n  nome: string\n  marca: string\n  modello: string\n  numero_serie: string\n  data_calibrazione: string\n  data_scadenza_calibrazione: string\n  note?: string\n  timestamp_creazione: string\n  timestamp_modifica?: string\n  tipo_strumento?: 'MEGGER' | 'MULTIMETRO' | 'OSCILLOSCOPIO' | 'ALTRO'\n  ente_certificatore?: string\n  numero_certificato_calibrazione?: string\n  range_misura?: string\n  precisione?: string\n  stato_strumento?: 'ATTIVO' | 'SCADUTO' | 'FUORI_SERVIZIO'\n}\n\nexport async function GET(\n  request: NextRequest,\n  { params }: { params: { cantiereId: string } }\n) {\n  try {\n    const cantiereId = parseInt(params.cantiereId)\n    \n    if (isNaN(cantiereId)) {\n      return NextResponse.json(\n        { error: 'ID cantiere non valido' },\n        { status: 400 }\n      )\n    }\n\n    // Ottieni il token di autenticazione dalla richiesta\n    const authHeader = request.headers.get('authorization')\n    if (!authHeader) {\n      return NextResponse.json(\n        { error: 'Token di autenticazione richiesto' },\n        { status: 401 }\n      )\n    }\n\n    // Chiama l'API backend Python\n    const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8001'\n    const response = await fetch(\n      `${backendUrl}/api/cantieri/${cantiereId}/strumenti`,\n      {\n        method: 'GET',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': authHeader,\n        },\n      }\n    )\n\n    if (!response.ok) {\n      const errorData = await response.json().catch(() => ({ detail: 'Errore sconosciuto' }))\n      return NextResponse.json(\n        { error: errorData.detail || 'Errore dal backend' },\n        { status: response.status }\n      )\n    }\n\n    const data = await response.json()\n\n    return NextResponse.json({\n      success: true,\n      data: data,\n      total: data.length\n    })\n\n  } catch (error) {\n    console.error('Errore nel recupero strumenti:', error)\n    return NextResponse.json(\n      { error: 'Errore interno del server' },\n      { status: 500 }\n    )\n  }\n}\n\nexport async function POST(\n  request: NextRequest,\n  { params }: { params: { cantiereId: string } }\n) {\n  try {\n    const cantiereId = parseInt(params.cantiereId)\n    const body = await request.json()\n    \n    if (isNaN(cantiereId)) {\n      return NextResponse.json(\n        { error: 'ID cantiere non valido' },\n        { status: 400 }\n      )\n    }\n\n    // Ottieni il token di autenticazione dalla richiesta\n    const authHeader = request.headers.get('authorization')\n    if (!authHeader) {\n      return NextResponse.json(\n        { error: 'Token di autenticazione richiesto' },\n        { status: 401 }\n      )\n    }\n\n    // Validazione base\n    if (!body.nome || !body.marca || !body.modello || !body.numero_serie) {\n      return NextResponse.json(\n        { error: 'Campi obbligatori mancanti' },\n        { status: 400 }\n      )\n    }\n\n    // Chiama l'API backend Python\n    const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8001'\n    const response = await fetch(\n      `${backendUrl}/api/cantieri/${cantiereId}/strumenti`,\n      {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': authHeader,\n        },\n        body: JSON.stringify(body),\n      }\n    )\n\n    if (!response.ok) {\n      const errorData = await response.json().catch(() => ({ detail: 'Errore sconosciuto' }))\n      return NextResponse.json(\n        { error: errorData.detail || 'Errore dal backend' },\n        { status: response.status }\n      )\n    }\n\n    const data = await response.json()\n\n    return NextResponse.json({\n      success: true,\n      data: data\n    }, { status: 201 })\n\n  } catch (error) {\n    console.error('Errore nella creazione strumento:', error)\n    return NextResponse.json(\n      { error: 'Errore interno del server' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAsBO,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAAsC;IAE9C,IAAI;QACF,MAAM,aAAa,SAAS,OAAO,UAAU;QAE7C,IAAI,MAAM,aAAa;YACrB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAyB,GAClC;gBAAE,QAAQ;YAAI;QAElB;QAEA,qDAAqD;QACrD,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;QACvC,IAAI,CAAC,YAAY;YACf,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAoC,GAC7C;gBAAE,QAAQ;YAAI;QAElB;QAEA,8BAA8B;QAC9B,MAAM,aAAa,6DAAmC;QACtD,MAAM,WAAW,MAAM,MACrB,GAAG,WAAW,cAAc,EAAE,WAAW,UAAU,CAAC,EACpD;YACE,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,iBAAiB;YACnB;QACF;QAGF,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC;oBAAE,QAAQ;gBAAqB,CAAC;YACrF,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO,UAAU,MAAM,IAAI;YAAqB,GAClD;gBAAE,QAAQ,SAAS,MAAM;YAAC;QAE9B;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;YACN,OAAO,KAAK,MAAM;QACpB;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA4B,GACrC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,KACpB,OAAoB,EACpB,EAAE,MAAM,EAAsC;IAE9C,IAAI;QACF,MAAM,aAAa,SAAS,OAAO,UAAU;QAC7C,MAAM,OAAO,MAAM,QAAQ,IAAI;QAE/B,IAAI,MAAM,aAAa;YACrB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAyB,GAClC;gBAAE,QAAQ;YAAI;QAElB;QAEA,qDAAqD;QACrD,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;QACvC,IAAI,CAAC,YAAY;YACf,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAoC,GAC7C;gBAAE,QAAQ;YAAI;QAElB;QAEA,mBAAmB;QACnB,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK,OAAO,IAAI,CAAC,KAAK,YAAY,EAAE;YACpE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA6B,GACtC;gBAAE,QAAQ;YAAI;QAElB;QAEA,8BAA8B;QAC9B,MAAM,aAAa,6DAAmC;QACtD,MAAM,WAAW,MAAM,MACrB,GAAG,WAAW,cAAc,EAAE,WAAW,UAAU,CAAC,EACpD;YACE,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,iBAAiB;YACnB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAGF,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC;oBAAE,QAAQ;gBAAqB,CAAC;YACrF,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO,UAAU,MAAM,IAAI;YAAqB,GAClD;gBAAE,QAAQ,SAAS,MAAM;YAAC;QAE9B;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;QACR,GAAG;YAAE,QAAQ;QAAI;IAEnB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA4B,GACrC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}