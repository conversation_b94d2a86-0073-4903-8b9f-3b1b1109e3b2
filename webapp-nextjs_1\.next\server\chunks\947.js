"use strict";exports.id=947,exports.ids=[947],exports.modules={1359:(e,t,n)=>{n.d(t,{Oh:()=>a});var r=n(43210),o=0;function a(){r.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??i()),document.body.insertAdjacentElement("beforeend",e[1]??i()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function i(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},11273:(e,t,n)=>{n.d(t,{A:()=>i,q:()=>a});var r=n(43210),o=n(60687);function a(e,t){let n=r.createContext(t),a=e=>{let{children:t,...a}=e,i=r.useMemo(()=>a,Object.values(a));return(0,o.jsx)(n.Provider,{value:i,children:t})};return a.displayName=e+"Provider",[a,function(o){let a=r.useContext(n);if(a)return a;if(void 0!==t)return t;throw Error(`\`${o}\` must be used within \`${e}\``)}]}function i(e,t=[]){let n=[],a=()=>{let t=n.map(e=>r.createContext(e));return function(n){let o=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return a.scopeName=e,[function(t,a){let i=r.createContext(a),u=n.length;n=[...n,a];let c=t=>{let{scope:n,children:a,...c}=t,l=n?.[e]?.[u]||i,s=r.useMemo(()=>c,Object.values(c));return(0,o.jsx)(l.Provider,{value:s,children:a})};return c.displayName=t+"Provider",[c,function(n,o){let c=o?.[e]?.[u]||i,l=r.useContext(c);if(l)return l;if(void 0!==a)return a;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}(a,...t)]}},13495:(e,t,n)=>{n.d(t,{c:()=>o});var r=n(43210);function o(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},14163:(e,t,n)=>{n.d(t,{hO:()=>c,sG:()=>u});var r=n(43210),o=n(51215),a=n(8730),i=n(60687),u=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,a.TL)(`Primitive.${t}`),o=r.forwardRef((e,r)=>{let{asChild:o,...a}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(o?n:t,{...a,ref:r})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function c(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},25028:(e,t,n)=>{n.d(t,{Z:()=>c});var r=n(43210),o=n(51215),a=n(14163),i=n(66156),u=n(60687),c=r.forwardRef((e,t)=>{let{container:n,...c}=e,[l,s]=r.useState(!1);(0,i.N)(()=>s(!0),[]);let d=n||l&&globalThis?.document?.body;return d?o.createPortal((0,u.jsx)(a.sG.div,{...c,ref:t}),d):null});c.displayName="Portal"},31355:(e,t,n)=>{n.d(t,{qW:()=>f});var r,o=n(43210),a=n(70569),i=n(14163),u=n(98599),c=n(13495),l=n(60687),s="dismissableLayer.update",d=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),f=o.forwardRef((e,t)=>{let{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:f,onPointerDownOutside:m,onFocusOutside:h,onInteractOutside:y,onDismiss:g,...E}=e,b=o.useContext(d),[w,S]=o.useState(null),C=w?.ownerDocument??globalThis?.document,[,L]=o.useState({}),N=(0,u.s)(t,e=>S(e)),x=Array.from(b.layers),[R]=[...b.layersWithOutsidePointerEventsDisabled].slice(-1),P=x.indexOf(R),T=w?x.indexOf(w):-1,A=b.layersWithOutsidePointerEventsDisabled.size>0,M=T>=P,k=function(e,t=globalThis?.document){let n=(0,c.c)(e),r=o.useRef(!1),a=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!r.current){let r=function(){p("dismissableLayer.pointerDownOutside",n,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(t.removeEventListener("click",a.current),a.current=r,t.addEventListener("click",a.current,{once:!0})):r()}else t.removeEventListener("click",a.current);r.current=!1},o=window.setTimeout(()=>{t.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(o),t.removeEventListener("pointerdown",e),t.removeEventListener("click",a.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}(e=>{let t=e.target,n=[...b.branches].some(e=>e.contains(t));M&&!n&&(m?.(e),y?.(e),e.defaultPrevented||g?.())},C),O=function(e,t=globalThis?.document){let n=(0,c.c)(e),r=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!r.current&&p("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}(e=>{let t=e.target;![...b.branches].some(e=>e.contains(t))&&(h?.(e),y?.(e),e.defaultPrevented||g?.())},C);return!function(e,t=globalThis?.document){let n=(0,c.c)(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{T===b.layers.size-1&&(f?.(e),!e.defaultPrevented&&g&&(e.preventDefault(),g()))},C),o.useEffect(()=>{if(w)return n&&(0===b.layersWithOutsidePointerEventsDisabled.size&&(r=C.body.style.pointerEvents,C.body.style.pointerEvents="none"),b.layersWithOutsidePointerEventsDisabled.add(w)),b.layers.add(w),v(),()=>{n&&1===b.layersWithOutsidePointerEventsDisabled.size&&(C.body.style.pointerEvents=r)}},[w,C,n,b]),o.useEffect(()=>()=>{w&&(b.layers.delete(w),b.layersWithOutsidePointerEventsDisabled.delete(w),v())},[w,b]),o.useEffect(()=>{let e=()=>L({});return document.addEventListener(s,e),()=>document.removeEventListener(s,e)},[]),(0,l.jsx)(i.sG.div,{...E,ref:N,style:{pointerEvents:A?M?"auto":"none":void 0,...e.style},onFocusCapture:(0,a.m)(e.onFocusCapture,O.onFocusCapture),onBlurCapture:(0,a.m)(e.onBlurCapture,O.onBlurCapture),onPointerDownCapture:(0,a.m)(e.onPointerDownCapture,k.onPointerDownCapture)})});function v(){let e=new CustomEvent(s);document.dispatchEvent(e)}function p(e,t,n,{discrete:r}){let o=n.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?(0,i.hO)(o,a):o.dispatchEvent(a)}f.displayName="DismissableLayer",o.forwardRef((e,t)=>{let n=o.useContext(d),r=o.useRef(null),a=(0,u.s)(t,r);return o.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,l.jsx)(i.sG.div,{...e,ref:a})}).displayName="DismissableLayerBranch"},32547:(e,t,n)=>{n.d(t,{n:()=>d});var r=n(43210),o=n(98599),a=n(14163),i=n(13495),u=n(60687),c="focusScope.autoFocusOnMount",l="focusScope.autoFocusOnUnmount",s={bubbles:!1,cancelable:!0},d=r.forwardRef((e,t)=>{let{loop:n=!1,trapped:d=!1,onMountAutoFocus:h,onUnmountAutoFocus:y,...g}=e,[E,b]=r.useState(null),w=(0,i.c)(h),S=(0,i.c)(y),C=r.useRef(null),L=(0,o.s)(t,e=>b(e)),N=r.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;r.useEffect(()=>{if(d){let e=function(e){if(N.paused||!E)return;let t=e.target;E.contains(t)?C.current=t:p(C.current,{select:!0})},t=function(e){if(N.paused||!E)return;let t=e.relatedTarget;null!==t&&(E.contains(t)||p(C.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&p(E)});return E&&n.observe(E,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[d,E,N.paused]),r.useEffect(()=>{if(E){m.add(N);let e=document.activeElement;if(!E.contains(e)){let t=new CustomEvent(c,s);E.addEventListener(c,w),E.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let n=document.activeElement;for(let r of e)if(p(r,{select:t}),document.activeElement!==n)return}(f(E).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&p(E))}return()=>{E.removeEventListener(c,w),setTimeout(()=>{let t=new CustomEvent(l,s);E.addEventListener(l,S),E.dispatchEvent(t),t.defaultPrevented||p(e??document.body,{select:!0}),E.removeEventListener(l,S),m.remove(N)},0)}}},[E,w,S,N]);let x=r.useCallback(e=>{if(!n&&!d||N.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,r=document.activeElement;if(t&&r){let t=e.currentTarget,[o,a]=function(e){let t=f(e);return[v(t,e),v(t.reverse(),e)]}(t);o&&a?e.shiftKey||r!==a?e.shiftKey&&r===o&&(e.preventDefault(),n&&p(a,{select:!0})):(e.preventDefault(),n&&p(o,{select:!0})):r===t&&e.preventDefault()}},[n,d,N.paused]);return(0,u.jsx)(a.sG.div,{tabIndex:-1,...g,ref:L,onKeyDown:x})});function f(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function v(e,t){for(let n of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function p(e,{select:t=!1}={}){if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}d.displayName="FocusScope";var m=function(){let e=[];return{add(t){let n=e[0];t!==n&&n?.pause(),(e=h(e,t)).unshift(t)},remove(t){e=h(e,t),e[0]?.resume()}}}();function h(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}},41862:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},42247:(e,t,n)=>{n.d(t,{A:()=>G});var r,o,a=function(){return(a=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function i(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}Object.create;Object.create;var u=("function"==typeof SuppressedError&&SuppressedError,n(43210)),c="right-scroll-bar-position",l="width-before-scroll-bar";function s(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var d="undefined"!=typeof window?u.useLayoutEffect:u.useEffect,f=new WeakMap;function v(e){return e}var p=function(e){void 0===e&&(e={});var t,n,r,o,i=(t=null,void 0===n&&(n=v),r=[],o=!1,{read:function(){if(o)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var t=n(e,o);return r.push(t),function(){r=r.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(o=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){o=!0;var t=[];if(r.length){var n=r;r=[],n.forEach(e),t=r}var a=function(){var n=t;t=[],n.forEach(e)},i=function(){return Promise.resolve().then(a)};i(),r={push:function(e){t.push(e),i()},filter:function(e){return t=t.filter(e),r}}}});return i.options=a({async:!0,ssr:!1},e),i}(),m=function(){},h=u.forwardRef(function(e,t){var n,r,o,c,l=u.useRef(null),v=u.useState({onScrollCapture:m,onWheelCapture:m,onTouchMoveCapture:m}),h=v[0],y=v[1],g=e.forwardProps,E=e.children,b=e.className,w=e.removeScrollBar,S=e.enabled,C=e.shards,L=e.sideCar,N=e.noRelative,x=e.noIsolation,R=e.inert,P=e.allowPinchZoom,T=e.as,A=e.gapMode,M=i(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),k=(n=[l,t],r=function(e){return n.forEach(function(t){return s(t,e)})},(o=(0,u.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,c=o.facade,d(function(){var e=f.get(c);if(e){var t=new Set(e),r=new Set(n),o=c.current;t.forEach(function(e){r.has(e)||s(e,null)}),r.forEach(function(e){t.has(e)||s(e,o)})}f.set(c,n)},[n]),c),O=a(a({},M),h);return u.createElement(u.Fragment,null,S&&u.createElement(L,{sideCar:p,removeScrollBar:w,shards:C,noRelative:N,noIsolation:x,inert:R,setCallbacks:y,allowPinchZoom:!!P,lockRef:l,gapMode:A}),g?u.cloneElement(u.Children.only(E),a(a({},O),{ref:k})):u.createElement(void 0===T?"div":T,a({},O,{className:b,ref:k}),E))});h.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},h.classNames={fullWidth:l,zeroRight:c};var y=function(e){var t=e.sideCar,n=i(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return u.createElement(r,a({},n))};y.isSideCarExport=!0;var g=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=o||n.nc;return t&&e.setAttribute("nonce",t),e}())){var a,i;(a=t).styleSheet?a.styleSheet.cssText=r:a.appendChild(document.createTextNode(r)),i=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(i)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},E=function(){var e=g();return function(t,n){u.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},b=function(){var e=E();return function(t){return e(t.styles,t.dynamic),null}},w={left:0,top:0,right:0,gap:0},S=function(e){return parseInt(e||"",10)||0},C=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[S(n),S(r),S(o)]},L=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return w;var t=C(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},N=b(),x="data-scroll-locked",R=function(e,t,n,r){var o=e.left,a=e.top,i=e.right,u=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(u,"px ").concat(r,";\n  }\n  body[").concat(x,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(a,"px;\n    padding-right: ").concat(i,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(u,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(u,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(c," {\n    right: ").concat(u,"px ").concat(r,";\n  }\n  \n  .").concat(l," {\n    margin-right: ").concat(u,"px ").concat(r,";\n  }\n  \n  .").concat(c," .").concat(c," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(l," .").concat(l," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(x,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(u,"px;\n  }\n")},P=function(){var e=parseInt(document.body.getAttribute(x)||"0",10);return isFinite(e)?e:0},T=function(){u.useEffect(function(){return document.body.setAttribute(x,(P()+1).toString()),function(){var e=P()-1;e<=0?document.body.removeAttribute(x):document.body.setAttribute(x,e.toString())}},[])},A=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;T();var a=u.useMemo(function(){return L(o)},[o]);return u.createElement(N,{styles:R(a,!t,o,n?"":"!important")})},M=!1;if("undefined"!=typeof window)try{var k=Object.defineProperty({},"passive",{get:function(){return M=!0,!0}});window.addEventListener("test",k,k),window.removeEventListener("test",k,k)}catch(e){M=!1}var O=!!M&&{passive:!1},D=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},W=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),j(e,r)){var o=I(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},j=function(e,t){return"v"===e?D(t,"overflowY"):D(t,"overflowX")},I=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},F=function(e,t,n,r,o){var a,i=(a=window.getComputedStyle(t).direction,"h"===e&&"rtl"===a?-1:1),u=i*r,c=n.target,l=t.contains(c),s=!1,d=u>0,f=0,v=0;do{if(!c)break;var p=I(e,c),m=p[0],h=p[1]-p[2]-i*m;(m||h)&&j(e,c)&&(f+=h,v+=m);var y=c.parentNode;c=y&&y.nodeType===Node.DOCUMENT_FRAGMENT_NODE?y.host:y}while(!l&&c!==document.body||l&&(t.contains(c)||t===c));return d&&(o&&1>Math.abs(f)||!o&&u>f)?s=!0:!d&&(o&&1>Math.abs(v)||!o&&-u>v)&&(s=!0),s},B=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},$=function(e){return[e.deltaX,e.deltaY]},_=function(e){return e&&"current"in e?e.current:e},K=0,X=[];let Y=(r=function(e){var t=u.useRef([]),n=u.useRef([0,0]),r=u.useRef(),o=u.useState(K++)[0],a=u.useState(b)[0],i=u.useRef(e);u.useEffect(function(){i.current=e},[e]),u.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,a=t.length;o<a;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(_),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var c=u.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!i.current.allowPinchZoom;var o,a=B(e),u=n.current,c="deltaX"in e?e.deltaX:u[0]-a[0],l="deltaY"in e?e.deltaY:u[1]-a[1],s=e.target,d=Math.abs(c)>Math.abs(l)?"h":"v";if("touches"in e&&"h"===d&&"range"===s.type)return!1;var f=W(d,s);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=W(d,s)),!f)return!1;if(!r.current&&"changedTouches"in e&&(c||l)&&(r.current=o),!o)return!0;var v=r.current||o;return F(v,t,e,"h"===v?c:l,!0)},[]),l=u.useCallback(function(e){if(X.length&&X[X.length-1]===a){var n="deltaY"in e?$(e):B(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta,r[0]===n[0]&&r[1]===n[1])})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(i.current.shards||[]).map(_).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?c(e,o[0]):!i.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),s=u.useCallback(function(e,n,r,o){var a={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(a),setTimeout(function(){t.current=t.current.filter(function(e){return e!==a})},1)},[]),d=u.useCallback(function(e){n.current=B(e),r.current=void 0},[]),f=u.useCallback(function(t){s(t.type,$(t),t.target,c(t,e.lockRef.current))},[]),v=u.useCallback(function(t){s(t.type,B(t),t.target,c(t,e.lockRef.current))},[]);u.useEffect(function(){return X.push(a),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:v}),document.addEventListener("wheel",l,O),document.addEventListener("touchmove",l,O),document.addEventListener("touchstart",d,O),function(){X=X.filter(function(e){return e!==a}),document.removeEventListener("wheel",l,O),document.removeEventListener("touchmove",l,O),document.removeEventListener("touchstart",d,O)}},[]);var p=e.removeScrollBar,m=e.inert;return u.createElement(u.Fragment,null,m?u.createElement(a,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,p?u.createElement(A,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},p.useMedium(r),y);var z=u.forwardRef(function(e,t){return u.createElement(h,a({},e,{ref:t,sideCar:Y}))});z.classNames=h.classNames;let G=z},63376:(e,t,n)=>{n.d(t,{Eq:()=>s});var r=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},o=new WeakMap,a=new WeakMap,i={},u=0,c=function(e){return e&&(e.host||c(e.parentNode))},l=function(e,t,n,r){var l=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=c(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});i[n]||(i[n]=new WeakMap);var s=i[n],d=[],f=new Set,v=new Set(l),p=function(e){!e||f.has(e)||(f.add(e),p(e.parentNode))};l.forEach(p);var m=function(e){!e||v.has(e)||Array.prototype.forEach.call(e.children,function(e){if(f.has(e))m(e);else try{var t=e.getAttribute(r),i=null!==t&&"false"!==t,u=(o.get(e)||0)+1,c=(s.get(e)||0)+1;o.set(e,u),s.set(e,c),d.push(e),1===u&&i&&a.set(e,!0),1===c&&e.setAttribute(n,"true"),i||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return m(t),f.clear(),u++,function(){d.forEach(function(e){var t=o.get(e)-1,i=s.get(e)-1;o.set(e,t),s.set(e,i),t||(a.has(e)||e.removeAttribute(r),a.delete(e)),i||e.removeAttribute(n)}),--u||(o=new WeakMap,o=new WeakMap,a=new WeakMap,i={})}},s=function(e,t,n){void 0===n&&(n="data-aria-hidden");var o=Array.from(Array.isArray(e)?e:[e]),a=t||r(e);return a?(o.push.apply(o,Array.from(a.querySelectorAll("[aria-live], script"))),l(o,a,n,"aria-hidden")):function(){return null}}},65551:(e,t,n)=>{n.d(t,{i:()=>u});var r,o=n(43210),a=n(66156),i=(r||(r=n.t(o,2)))[" useInsertionEffect ".trim().toString()]||a.N;function u({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[a,u,c]=function({defaultProp:e,onChange:t}){let[n,r]=o.useState(e),a=o.useRef(n),u=o.useRef(t);return i(()=>{u.current=t},[t]),o.useEffect(()=>{a.current!==n&&(u.current?.(n),a.current=n)},[n,a]),[n,r,u]}({defaultProp:t,onChange:n}),l=void 0!==e,s=l?e:a;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==l){let t=l?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=l},[l,r])}return[s,o.useCallback(t=>{if(l){let n="function"==typeof t?t(e):t;n!==e&&c.current?.(n)}else u(t)},[l,e,u,c])]}Symbol("RADIX:SYNC_STATE")},66156:(e,t,n)=>{n.d(t,{N:()=>o});var r=n(43210),o=globalThis?.document?r.useLayoutEffect:()=>{}},70569:(e,t,n)=>{n.d(t,{m:()=>r});function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}},78148:(e,t,n)=>{n.d(t,{b:()=>u});var r=n(43210),o=n(14163),a=n(60687),i=r.forwardRef((e,t)=>(0,a.jsx)(o.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));i.displayName="Label";var u=i},96963:(e,t,n)=>{n.d(t,{B:()=>c});var r,o=n(43210),a=n(66156),i=(r||(r=n.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),u=0;function c(e){let[t,n]=o.useState(i());return(0,a.N)(()=>{e||n(e=>e??String(u++))},[e]),e||(t?`radix-${t}`:"")}}};