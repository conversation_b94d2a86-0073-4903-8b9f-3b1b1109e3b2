"use strict";exports.id=203,exports.ids=[203],exports.modules={26134:(e,t,n)=>{n.d(t,{UC:()=>en,VY:()=>eo,ZL:()=>ee,bL:()=>Q,bm:()=>ei,hE:()=>er,hJ:()=>et,l9:()=>X});var r=n(43210),o=n(70569),i=n(98599),a=n(11273),s=n(96963),l=n(65551),u=n(31355),d=n(32547),c=n(25028),f=n(46059),p=n(14163),m=n(1359),g=n(42247),y=n(63376),N=n(8730),v=n(60687),D="Dialog",[O,h]=(0,a.A)(D),[R,x]=O(D),j=e=>{let{__scopeDialog:t,children:n,open:o,defaultOpen:i,onOpenChange:a,modal:u=!0}=e,d=r.useRef(null),c=r.useRef(null),[f,p]=(0,l.i)({prop:o,defaultProp:i??!1,onChange:a,caller:D});return(0,v.jsx)(R,{scope:t,triggerRef:d,contentRef:c,contentId:(0,s.B)(),titleId:(0,s.B)(),descriptionId:(0,s.B)(),open:f,onOpenChange:p,onOpenToggle:r.useCallback(()=>p(e=>!e),[p]),modal:u,children:n})};j.displayName=D;var I="DialogTrigger",w=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,a=x(I,n),s=(0,i.s)(t,a.triggerRef);return(0,v.jsx)(p.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":a.open,"aria-controls":a.contentId,"data-state":Z(a.open),...r,ref:s,onClick:(0,o.m)(e.onClick,a.onOpenToggle)})});w.displayName=I;var C="DialogPortal",[b,E]=O(C,{forceMount:void 0}),A=e=>{let{__scopeDialog:t,forceMount:n,children:o,container:i}=e,a=x(C,t);return(0,v.jsx)(b,{scope:t,forceMount:n,children:r.Children.map(o,e=>(0,v.jsx)(f.C,{present:n||a.open,children:(0,v.jsx)(c.Z,{asChild:!0,container:i,children:e})}))})};A.displayName=C;var T="DialogOverlay",M=r.forwardRef((e,t)=>{let n=E(T,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=x(T,e.__scopeDialog);return i.modal?(0,v.jsx)(f.C,{present:r||i.open,children:(0,v.jsx)(_,{...o,ref:t})}):null});M.displayName=T;var F=(0,N.TL)("DialogOverlay.RemoveScroll"),_=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=x(T,n);return(0,v.jsx)(g.A,{as:F,allowPinchZoom:!0,shards:[o.contentRef],children:(0,v.jsx)(p.sG.div,{"data-state":Z(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),P="DialogContent",U=r.forwardRef((e,t)=>{let n=E(P,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=x(P,e.__scopeDialog);return(0,v.jsx)(f.C,{present:r||i.open,children:i.modal?(0,v.jsx)(L,{...o,ref:t}):(0,v.jsx)(k,{...o,ref:t})})});U.displayName=P;var L=r.forwardRef((e,t)=>{let n=x(P,e.__scopeDialog),a=r.useRef(null),s=(0,i.s)(t,n.contentRef,a);return r.useEffect(()=>{let e=a.current;if(e)return(0,y.Eq)(e)},[]),(0,v.jsx)(S,{...e,ref:s,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),n.triggerRef.current?.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),k=r.forwardRef((e,t)=>{let n=x(P,e.__scopeDialog),o=r.useRef(!1),i=r.useRef(!1);return(0,v.jsx)(S,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(o.current||n.triggerRef.current?.focus(),t.preventDefault()),o.current=!1,i.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(o.current=!0,"pointerdown"===t.detail.originalEvent.type&&(i.current=!0));let r=t.target;n.triggerRef.current?.contains(r)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&i.current&&t.preventDefault()}})}),S=r.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:o,onOpenAutoFocus:a,onCloseAutoFocus:s,...l}=e,c=x(P,n),f=r.useRef(null),p=(0,i.s)(t,f);return(0,m.Oh)(),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(d.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:a,onUnmountAutoFocus:s,children:(0,v.jsx)(u.qW,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":Z(c.open),...l,ref:p,onDismiss:()=>c.onOpenChange(!1)})}),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(Y,{titleId:c.titleId}),(0,v.jsx)(z,{contentRef:f,descriptionId:c.descriptionId})]})]})}),W="DialogTitle",B=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=x(W,n);return(0,v.jsx)(p.sG.h2,{id:o.titleId,...r,ref:t})});B.displayName=W;var G="DialogDescription",$=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=x(G,n);return(0,v.jsx)(p.sG.p,{id:o.descriptionId,...r,ref:t})});$.displayName=G;var q="DialogClose",V=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,i=x(q,n);return(0,v.jsx)(p.sG.button,{type:"button",...r,ref:t,onClick:(0,o.m)(e.onClick,()=>i.onOpenChange(!1))})});function Z(e){return e?"open":"closed"}V.displayName=q;var H="DialogTitleWarning",[J,K]=(0,a.q)(H,{contentName:P,titleName:W,docsSlug:"dialog"}),Y=({titleId:e})=>{let t=K(H),n=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return r.useEffect(()=>{e&&(document.getElementById(e)||console.error(n))},[n,e]),null},z=({contentRef:e,descriptionId:t})=>{let n=K("DialogDescriptionWarning"),o=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${n.contentName}}.`;return r.useEffect(()=>{let n=e.current?.getAttribute("aria-describedby");t&&n&&(document.getElementById(t)||console.warn(o))},[o,e,t]),null},Q=j,X=w,ee=A,et=M,en=U,er=B,eo=$,ei=V},46059:(e,t,n)=>{n.d(t,{C:()=>a});var r=n(43210),o=n(98599),i=n(66156),a=e=>{let{present:t,children:n}=e,a=function(e){var t,n;let[o,a]=r.useState(),l=r.useRef(null),u=r.useRef(e),d=r.useRef("none"),[c,f]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>n[e][t]??e,t));return r.useEffect(()=>{let e=s(l.current);d.current="mounted"===c?e:"none"},[c]),(0,i.N)(()=>{let t=l.current,n=u.current;if(n!==e){let r=d.current,o=s(t);e?f("MOUNT"):"none"===o||t?.display==="none"?f("UNMOUNT"):n&&r!==o?f("ANIMATION_OUT"):f("UNMOUNT"),u.current=e}},[e,f]),(0,i.N)(()=>{if(o){let e,t=o.ownerDocument.defaultView??window,n=n=>{let r=s(l.current).includes(n.animationName);if(n.target===o&&r&&(f("ANIMATION_END"),!u.current)){let n=o.style.animationFillMode;o.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=n)})}},r=e=>{e.target===o&&(d.current=s(l.current))};return o.addEventListener("animationstart",r),o.addEventListener("animationcancel",n),o.addEventListener("animationend",n),()=>{t.clearTimeout(e),o.removeEventListener("animationstart",r),o.removeEventListener("animationcancel",n),o.removeEventListener("animationend",n)}}f("ANIMATION_END")},[o,f]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:r.useCallback(e=>{l.current=e?getComputedStyle(e):null,a(e)},[])}}(t),l="function"==typeof n?n({present:a.isPresent}):r.Children.only(n),u=(0,o.s)(a.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(n=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(l));return"function"==typeof n||a.isPresent?r.cloneElement(l,{ref:u}):null};function s(e){return e?.animationName||"none"}a.displayName="Presence"},99270:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])}};