{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/app/api/parco-cavi/%5BcantiereId%5D/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\n\nexport async function GET(\n  request: NextRequest,\n  { params }: { params: { cantiereId: string } }\n) {\n  try {\n    const cantiereId = params.cantiereId\n    \n    // Estrai il token di autorizzazione dall'header\n    const authHeader = request.headers.get('authorization')\n    \n    if (!authHeader || !authHeader.startsWith('Bearer ')) {\n      return NextResponse.json(\n        { \n          detail: 'Token di autorizzazione mancante' \n        }, \n        { status: 401 }\n      )\n    }\n\n    // Estrai i parametri di query\n    const { searchParams } = new URL(request.url)\n    const queryParams = new URLSearchParams()\n    \n    // Passa tutti i parametri al backend\n    searchParams.forEach((value, key) => {\n      queryParams.append(key, value)\n    })\n\n    // Proxy la richiesta al backend FastAPI\n    const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8001'\n    const queryString = queryParams.toString()\n    const url = `${backendUrl}/api/parco-cavi/${cantiereId}${queryString ? `?${queryString}` : ''}`\n    \n    console.log('🔄 Parco-Cavi API: Proxying request to backend:', url)\n    \n    const response = await fetch(url, {\n      method: 'GET',\n      headers: {\n        'Content-Type': 'application/json',\n        'Authorization': authHeader\n      }\n    })\n\n    console.log('📡 Parco-Cavi API: Backend response status:', response.status)\n\n    if (!response.ok) {\n      const errorData = await response.json().catch(() => ({ detail: 'Errore sconosciuto' }))\n      console.error('❌ Parco-Cavi API: Backend error:', errorData)\n      return NextResponse.json(errorData, { \n        status: response.status \n      })\n    }\n\n    const data = await response.json()\n    console.log('📡 Parco-Cavi API: Backend response data:', data)\n\n    // Il backend restituisce direttamente l'array, quindi lo restituiamo così com'è\n    return NextResponse.json(data, { \n      status: response.status,\n      headers: {\n        'Content-Type': 'application/json'\n      }\n    })\n\n  } catch (error) {\n    console.error('❌ Parco-Cavi API: Error:', error)\n    return NextResponse.json(\n      { \n        detail: 'Errore interno del server' \n      }, \n      { status: 500 }\n    )\n  }\n}\n\nexport async function POST(\n  request: NextRequest,\n  { params }: { params: { cantiereId: string } }\n) {\n  try {\n    const cantiereId = params.cantiereId\n    const body = await request.json()\n    \n    // Estrai il token di autorizzazione dall'header\n    const authHeader = request.headers.get('authorization')\n    \n    if (!authHeader || !authHeader.startsWith('Bearer ')) {\n      return NextResponse.json(\n        { \n          detail: 'Token di autorizzazione mancante' \n        }, \n        { status: 401 }\n      )\n    }\n\n    // Proxy la richiesta al backend FastAPI\n    const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8001'\n    \n    console.log('🔄 Parco-Cavi API: Proxying POST request to backend:', `${backendUrl}/api/parco-cavi/${cantiereId}`)\n    \n    const response = await fetch(`${backendUrl}/api/parco-cavi/${cantiereId}`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'Authorization': authHeader\n      },\n      body: JSON.stringify(body)\n    })\n\n    console.log('📡 Parco-Cavi API: Backend response status:', response.status)\n\n    if (!response.ok) {\n      const errorData = await response.json().catch(() => ({ detail: 'Errore sconosciuto' }))\n      console.error('❌ Parco-Cavi API: Backend error:', errorData)\n      return NextResponse.json(errorData, { \n        status: response.status \n      })\n    }\n\n    const data = await response.json()\n    console.log('📡 Parco-Cavi API: Backend response data:', data)\n\n    return NextResponse.json(data, { \n      status: response.status,\n      headers: {\n        'Content-Type': 'application/json'\n      }\n    })\n\n  } catch (error) {\n    console.error('❌ Parco-Cavi API: POST Error:', error)\n    return NextResponse.json(\n      { \n        detail: 'Errore interno del server' \n      }, \n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAEO,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAAsC;IAE9C,IAAI;QACF,MAAM,aAAa,OAAO,UAAU;QAEpC,gDAAgD;QAChD,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;QAEvC,IAAI,CAAC,cAAc,CAAC,WAAW,UAAU,CAAC,YAAY;YACpD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,QAAQ;YACV,GACA;gBAAE,QAAQ;YAAI;QAElB;QAEA,8BAA8B;QAC9B,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,cAAc,IAAI;QAExB,qCAAqC;QACrC,aAAa,OAAO,CAAC,CAAC,OAAO;YAC3B,YAAY,MAAM,CAAC,KAAK;QAC1B;QAEA,wCAAwC;QACxC,MAAM,aAAa,6DAAmC;QACtD,MAAM,cAAc,YAAY,QAAQ;QACxC,MAAM,MAAM,GAAG,WAAW,gBAAgB,EAAE,aAAa,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;QAE/F,QAAQ,GAAG,CAAC,mDAAmD;QAE/D,MAAM,WAAW,MAAM,MAAM,KAAK;YAChC,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,iBAAiB;YACnB;QACF;QAEA,QAAQ,GAAG,CAAC,+CAA+C,SAAS,MAAM;QAE1E,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC;oBAAE,QAAQ;gBAAqB,CAAC;YACrF,QAAQ,KAAK,CAAC,oCAAoC;YAClD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,WAAW;gBAClC,QAAQ,SAAS,MAAM;YACzB;QACF;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,QAAQ,GAAG,CAAC,6CAA6C;QAEzD,gFAAgF;QAChF,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,MAAM;YAC7B,QAAQ,SAAS,MAAM;YACvB,SAAS;gBACP,gBAAgB;YAClB;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,QAAQ;QACV,GACA;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,KACpB,OAAoB,EACpB,EAAE,MAAM,EAAsC;IAE9C,IAAI;QACF,MAAM,aAAa,OAAO,UAAU;QACpC,MAAM,OAAO,MAAM,QAAQ,IAAI;QAE/B,gDAAgD;QAChD,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;QAEvC,IAAI,CAAC,cAAc,CAAC,WAAW,UAAU,CAAC,YAAY;YACpD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,QAAQ;YACV,GACA;gBAAE,QAAQ;YAAI;QAElB;QAEA,wCAAwC;QACxC,MAAM,aAAa,6DAAmC;QAEtD,QAAQ,GAAG,CAAC,wDAAwD,GAAG,WAAW,gBAAgB,EAAE,YAAY;QAEhH,MAAM,WAAW,MAAM,MAAM,GAAG,WAAW,gBAAgB,EAAE,YAAY,EAAE;YACzE,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,iBAAiB;YACnB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,QAAQ,GAAG,CAAC,+CAA+C,SAAS,MAAM;QAE1E,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC;oBAAE,QAAQ;gBAAqB,CAAC;YACrF,QAAQ,KAAK,CAAC,oCAAoC;YAClD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,WAAW;gBAClC,QAAQ,SAAS,MAAM;YACzB;QACF;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,QAAQ,GAAG,CAAC,6CAA6C;QAEzD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,MAAM;YAC7B,QAAQ,SAAS,MAAM;YACvB,SAAS;gBACP,gBAAgB;YAClB;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,QAAQ;QACV,GACA;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}