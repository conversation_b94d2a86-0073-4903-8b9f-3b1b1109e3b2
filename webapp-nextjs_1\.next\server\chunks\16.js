"use strict";exports.id=16,exports.ids=[16],exports.modules={15079:(e,a,s)=>{s.d(a,{bq:()=>m,eb:()=>u,gC:()=>x,l6:()=>d,yv:()=>c});var t=s(60687);s(43210);var r=s(97822),i=s(78272),l=s(13964),n=s(3589),o=s(4780);function d({...e}){return(0,t.jsx)(r.bL,{"data-slot":"select",...e})}function c({...e}){return(0,t.jsx)(r.WT,{"data-slot":"select-value",...e})}function m({className:e,size:a="default",children:s,...l}){return(0,t.jsxs)(r.l9,{"data-slot":"select-trigger","data-size":a,className:(0,o.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...l,children:[s,(0,t.jsx)(r.In,{asChild:!0,children:(0,t.jsx)(i.A,{className:"size-4 opacity-50"})})]})}function x({className:e,children:a,position:s="popper",...i}){return(0,t.jsx)(r.ZL,{children:(0,t.jsxs)(r.UC,{"data-slot":"select-content",className:(0,o.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:s,...i,children:[(0,t.jsx)(p,{}),(0,t.jsx)(r.LM,{className:(0,o.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:a}),(0,t.jsx)(v,{})]})})}function u({className:e,children:a,...s}){return(0,t.jsxs)(r.q7,{"data-slot":"select-item",className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...s,children:[(0,t.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,t.jsx)(r.VF,{children:(0,t.jsx)(l.A,{className:"size-4"})})}),(0,t.jsx)(r.p4,{children:a})]})}function p({className:e,...a}){return(0,t.jsx)(r.PP,{"data-slot":"select-scroll-up-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...a,children:(0,t.jsx)(n.A,{className:"size-4"})})}function v({className:e,...a}){return(0,t.jsx)(r.wn,{"data-slot":"select-scroll-down-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...a,children:(0,t.jsx)(i.A,{className:"size-4"})})}},34729:(e,a,s)=>{s.d(a,{T:()=>l});var t=s(60687),r=s(43210),i=s(4780);let l=r.forwardRef(({className:e,...a},s)=>(0,t.jsx)("textarea",{className:(0,i.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:s,...a}));l.displayName="Textarea"},44493:(e,a,s)=>{s.d(a,{BT:()=>o,Wu:()=>d,ZB:()=>n,Zp:()=>i,aR:()=>l});var t=s(60687);s(43210);var r=s(4780);function i({className:e,...a}){return(0,t.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...a})}function l({className:e,...a}){return(0,t.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...a})}function n({className:e,...a}){return(0,t.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",e),...a})}function o({className:e,...a}){return(0,t.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",e),...a})}function d({className:e,...a}){return(0,t.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",e),...a})}},70440:(e,a,s)=>{s.r(a),s.d(a,{default:()=>r});var t=s(31658);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,t.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},80013:(e,a,s)=>{s.d(a,{J:()=>l});var t=s(60687);s(43210);var r=s(78148),i=s(4780);function l({className:e,...a}){return(0,t.jsx)(r.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...a})}},89667:(e,a,s)=>{s.d(a,{p:()=>l});var t=s(60687),r=s(43210),i=s(4780);let l=r.forwardRef(({className:e,type:a,...s},r)=>(0,t.jsx)("input",{type:a,"data-slot":"input",className:(0,i.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),ref:r,...s}));l.displayName="Input"},91136:(e,a,s)=>{s.d(a,{A:()=>y});var t=s(60687),r=s(29523),i=s(91821),l=s(41862),n=s(10022),o=s(11860),d=s(93613),c=s(8819),m=s(43210),x=s(62185),u=s(80013),p=s(15079);function v({formData:e,cavi:a,responsabili:s,strumenti:r,validationErrors:i,isCavoLocked:l,onInputChange:n}){return(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(u.J,{htmlFor:"id_cavo",className:"text-sm font-medium text-gray-700",children:"Cavo *"}),(0,t.jsxs)(p.l6,{value:e.id_cavo,onValueChange:e=>n("id_cavo",e),disabled:l,children:[(0,t.jsx)(p.bq,{className:`h-11 text-sm ${i.id_cavo?"border-red-500":"border-gray-300"}`,children:(0,t.jsx)(p.yv,{placeholder:"Seleziona cavo..."})}),(0,t.jsx)(p.gC,{children:a.map(e=>(0,t.jsx)(p.eb,{value:e.id_cavo,children:(0,t.jsxs)("div",{className:"flex flex-col py-1",children:[(0,t.jsx)("span",{className:"font-medium text-sm",children:e.id_cavo}),(0,t.jsxs)("span",{className:"text-xs text-gray-500",children:[e.tipologia," ",e.sezione]})]})},e.id_cavo))})]}),i.id_cavo&&(0,t.jsx)("p",{className:"text-sm text-red-600",children:i.id_cavo})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(u.J,{htmlFor:"id_operatore",className:"text-sm font-medium text-gray-700",children:"Operatore"}),(0,t.jsxs)(p.l6,{value:e.id_operatore?.toString()||"",onValueChange:e=>n("id_operatore",parseInt(e)),children:[(0,t.jsx)(p.bq,{className:"h-11 text-sm border-gray-300",children:(0,t.jsx)(p.yv,{placeholder:"Seleziona operatore..."})}),(0,t.jsx)(p.gC,{children:s.map(e=>(0,t.jsx)(p.eb,{value:e.id_responsabile.toString(),children:(0,t.jsx)("span",{className:"text-sm",children:e.nome_responsabile})},e.id_responsabile))})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(u.J,{htmlFor:"id_strumento",className:"text-sm font-medium text-gray-700",children:"Strumento di Misura"}),(0,t.jsxs)(p.l6,{value:e.id_strumento?.toString()||"",onValueChange:e=>{let a=r.find(a=>a.id_strumento===parseInt(e));n("id_strumento",parseInt(e)),a&&n("strumento_utilizzato",`${a.marca} ${a.modello}`)},children:[(0,t.jsx)(p.bq,{className:"h-11 text-sm border-gray-300",children:(0,t.jsx)(p.yv,{placeholder:"Seleziona strumento..."})}),(0,t.jsx)(p.gC,{children:r.map(e=>(0,t.jsx)(p.eb,{value:e.id_strumento.toString(),children:(0,t.jsxs)("div",{className:"flex flex-col py-1",children:[(0,t.jsx)("span",{className:"font-medium text-sm",children:e.nome}),(0,t.jsxs)("span",{className:"text-xs text-gray-500",children:[e.marca," ",e.modello]})]})},e.id_strumento))})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(u.J,{htmlFor:"tipo_certificato",className:"text-sm font-medium text-gray-700",children:"Tipo Certificato"}),(0,t.jsxs)(p.l6,{value:e.tipo_certificato||"SINGOLO",onValueChange:e=>n("tipo_certificato",e),children:[(0,t.jsx)(p.bq,{className:"h-11 text-sm border-gray-300",children:(0,t.jsx)(p.yv,{})}),(0,t.jsxs)(p.gC,{children:[(0,t.jsx)(p.eb,{value:"SINGOLO",children:(0,t.jsx)("span",{className:"text-sm",children:"\uD83D\uDD0D Singolo"})}),(0,t.jsx)(p.eb,{value:"GRUPPO",children:(0,t.jsx)("span",{className:"text-sm",children:"\uD83D\uDCCA Gruppo"})})]})]})]})]})}var h=s(89667),g=s(84027);function b({formData:e,weatherData:a,isLoadingWeather:s,isWeatherOverride:i,onInputChange:n,onToggleWeatherOverride:d}){return a?(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsx)("div",{className:`p-4 rounded-lg border-2 col-span-full ${a.isDemo?"bg-amber-50 border-amber-200":"bg-emerald-50 border-emerald-200"}`,children:(0,t.jsxs)("div",{className:"flex items-start gap-4",children:[s?(0,t.jsx)(l.A,{className:"h-5 w-5 animate-spin text-blue-600 mt-1"}):(0,t.jsx)("div",{className:"text-2xl",children:a.isDemo?"\uD83D\uDD27":"\uD83C\uDF24️"}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"text-lg font-semibold text-gray-900",children:[a.temperature,"\xb0C • ",a.humidity,"% UR"]}),a.city&&(0,t.jsx)("div",{className:"text-sm text-gray-600",children:a.city})]}),(0,t.jsx)(r.$,{type:"button",variant:i?"default":"outline",size:"sm",onClick:d,className:"h-8",children:i?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(o.A,{className:"h-3 w-3 mr-1"}),"Automatico"]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(g.A,{className:"h-3 w-3 mr-1"}),"Manuale"]})})]}),(0,t.jsxs)("div",{className:"text-xs text-gray-500",children:["\uD83D\uDCE1 ",a.source,a.isDemo&&" • Dati dimostrativi"]})]})]})}),i&&(0,t.jsxs)("div",{className:"col-span-full p-4 bg-blue-50 border-2 border-blue-200 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-4",children:[(0,t.jsx)(g.A,{className:"h-4 w-4 text-blue-600"}),(0,t.jsx)("span",{className:"text-sm font-medium text-blue-800",children:"Inserimento Manuale Parametri"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(u.J,{htmlFor:"temperatura_prova",className:"text-sm font-medium text-gray-700",children:"Temperatura (\xb0C)"}),(0,t.jsx)(h.p,{id:"temperatura_prova",type:"number",step:"0.1",value:e.temperatura_prova||"",onChange:e=>n("temperatura_prova",parseFloat(e.target.value)),placeholder:"20.0",className:"h-11 text-sm"}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:"Range tipico: 15-30\xb0C"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(u.J,{htmlFor:"umidita_prova",className:"text-sm font-medium text-gray-700",children:"Umidit\xe0 Relativa (%)"}),(0,t.jsx)(h.p,{id:"umidita_prova",type:"number",min:"0",max:"100",value:e.umidita_prova||"",onChange:e=>n("umidita_prova",parseFloat(e.target.value)),placeholder:"50",className:"h-11 text-sm"}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:"Range tipico: 30-70%"})]})]})]})]}):null}function f({formData:e,validationErrors:a,onInputChange:s}){return(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(u.J,{htmlFor:"valore_isolamento",className:"text-sm font-medium text-gray-700",children:"Isolamento (MΩ) *"}),(0,t.jsx)(h.p,{id:"valore_isolamento",type:"number",step:"0.01",value:e.valore_isolamento||"",onChange:e=>s("valore_isolamento",parseFloat(e.target.value)),placeholder:"≥ 1000",className:`h-11 text-sm ${a.valore_isolamento?"border-red-500":"border-gray-300"}`}),a.valore_isolamento&&(0,t.jsx)("p",{className:"text-sm text-red-600",children:a.valore_isolamento}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:"Valore minimo: 1000 MΩ"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(u.J,{htmlFor:"valore_continuita",className:"text-sm font-medium text-gray-700",children:"Test Continuit\xe0 *"}),(0,t.jsxs)(p.l6,{value:e.valore_continuita,onValueChange:e=>s("valore_continuita",e),children:[(0,t.jsx)(p.bq,{className:`h-11 text-sm ${a.valore_continuita?"border-red-500":"border-gray-300"}`,children:(0,t.jsx)(p.yv,{placeholder:"Seleziona esito..."})}),(0,t.jsxs)(p.gC,{children:[(0,t.jsx)(p.eb,{value:"CONFORME",children:(0,t.jsx)("span",{className:"text-sm",children:"✅ CONFORME"})}),(0,t.jsx)(p.eb,{value:"NON_CONFORME",children:(0,t.jsx)("span",{className:"text-sm",children:"❌ NON CONFORME"})})]})]}),a.valore_continuita&&(0,t.jsx)("p",{className:"text-sm text-red-600",children:a.valore_continuita})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(u.J,{htmlFor:"valore_resistenza",className:"text-sm font-medium text-gray-700",children:"Resistenza (Ω) *"}),(0,t.jsx)(h.p,{id:"valore_resistenza",type:"number",step:"0.01",value:e.valore_resistenza||"",onChange:e=>s("valore_resistenza",parseFloat(e.target.value)),placeholder:"< 1.0",className:`h-11 text-sm ${a.valore_resistenza?"border-red-500":"border-gray-300"}`}),a.valore_resistenza&&(0,t.jsx)("p",{className:"text-sm text-red-600",children:a.valore_resistenza}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:"Valore massimo: 1.0 Ω"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(u.J,{htmlFor:"tensione_prova_isolamento",className:"text-sm font-medium text-gray-700",children:"Tensione di Prova (V)"}),(0,t.jsx)(h.p,{id:"tensione_prova_isolamento",type:"number",value:e.tensione_prova_isolamento||500,onChange:e=>s("tensione_prova_isolamento",parseInt(e.target.value)),className:"h-11 text-sm border-gray-300"}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:"Standard: 500V DC"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(u.J,{htmlFor:"durata_prova",className:"text-sm font-medium text-gray-700",children:"Durata Prova (min)"}),(0,t.jsx)(h.p,{id:"durata_prova",type:"number",value:e.durata_prova||1,onChange:e=>s("durata_prova",parseInt(e.target.value)),placeholder:"1",className:"h-11 text-sm border-gray-300"}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:"Standard: 1 minuto"})]})]})}var j=s(34729);function N({formData:e,onInputChange:a}){return(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"space-y-2 col-span-full",children:[(0,t.jsx)(u.J,{htmlFor:"note",className:"text-sm font-medium text-gray-700",children:"Note Aggiuntive"}),(0,t.jsx)(j.T,{id:"note",value:e.note||"",onChange:e=>a("note",e.target.value),placeholder:"Inserisci eventuali note, osservazioni o anomalie riscontrate durante la certificazione...",rows:4,className:"resize-none text-sm border-gray-300"}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:"Descrivi eventuali condizioni particolari o osservazioni rilevanti"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(u.J,{htmlFor:"stato_certificato",className:"text-sm font-medium text-gray-700",children:"Stato Certificazione"}),(0,t.jsxs)(p.l6,{value:e.stato_certificato||"BOZZA",onValueChange:e=>a("stato_certificato",e),children:[(0,t.jsx)(p.bq,{className:"h-11 text-sm border-gray-300",children:(0,t.jsx)(p.yv,{})}),(0,t.jsxs)(p.gC,{children:[(0,t.jsx)(p.eb,{value:"BOZZA",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"w-3 h-3 rounded-full bg-gray-400"}),(0,t.jsx)("span",{className:"text-sm",children:"Bozza"})]})}),(0,t.jsx)(p.eb,{value:"CONFORME",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"w-3 h-3 rounded-full bg-green-500"}),(0,t.jsx)("span",{className:"text-sm",children:"✅ Conforme"})]})}),(0,t.jsx)(p.eb,{value:"NON_CONFORME",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"w-3 h-3 rounded-full bg-red-500"}),(0,t.jsx)("span",{className:"text-sm",children:"❌ Non Conforme"})]})}),(0,t.jsx)(p.eb,{value:"CONFORME_CON_RISERVA",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"w-3 h-3 rounded-full bg-yellow-500"}),(0,t.jsx)("span",{className:"text-sm",children:"⚠️ Conforme con Riserva"})]})})]})]}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:"Lo stato verr\xe0 determinato automaticamente in base ai valori misurati"})]})]})}function y({cantiereId:e,certificazione:a,strumenti:s,preselectedCavoId:u,onSuccess:p,onCancel:h}){let{formData:g,cavi:j,responsabili:y,weatherData:_,isLoading:w,isSaving:C,isLoadingWeather:z,error:D,validationErrors:O,isWeatherOverride:S,isEdit:F,isCavoLocked:A,handleInputChange:I,handleSubmit:k,setIsWeatherOverride:R,onCancel:E}=function({cantiereId:e,certificazione:a,strumenti:s,preselectedCavoId:t,onSuccess:r,onCancel:i}){let[l,n]=(0,m.useState)({id_cantiere:e,tipo_certificato:"SINGOLO",stato_certificato:"BOZZA",valore_continuita:"CONFORME",tensione_prova_isolamento:500,temperatura_prova:20,umidita_prova:50}),[o,d]=(0,m.useState)(!0),[c,u]=(0,m.useState)(!1),[p,v]=(0,m.useState)(!1),[h,g]=(0,m.useState)(""),[b,f]=(0,m.useState)({}),[j,N]=(0,m.useState)(!1),[y,_]=(0,m.useState)([]),[w,C]=(0,m.useState)([]),[z,D]=(0,m.useState)(null),O=!!a;async function S(){if(!function(){let e={};return l.id_cavo||(e.id_cavo="Seleziona un cavo"),(!l.valore_isolamento||l.valore_isolamento<=0)&&(e.valore_isolamento="Inserisci un valore di isolamento valido"),l.valore_continuita||(e.valore_continuita="Seleziona il risultato della continuit\xe0"),l.valore_resistenza||(e.valore_resistenza="Inserisci un valore di resistenza"),f(e),0===Object.keys(e).length}())return void g("Correggi gli errori nel form prima di continuare");try{let s;u(!0),g("");let t=function(){let a={...l};return z&&!j&&(a.temperatura_prova=z.temperature,a.umidita_prova=z.humidity),a.id_cantiere=e,a.data_certificazione=a.data_certificazione||new Date().toISOString().split("T")[0],a}();(s=O&&a?await x.km.updateCertificazione(a.id_certificazione,t):await x.km.createCertificazione(t)).data&&r(s.data)}catch(e){console.error("Errore salvataggio:",e),g(e.response?.data?.detail||"Errore durante il salvataggio")}finally{u(!1)}}return{formData:l,cavi:y,responsabili:w,strumenti:s,weatherData:z,isLoading:o,isSaving:c,isLoadingWeather:p,error:h,validationErrors:b,isWeatherOverride:j,isEdit:O,isCavoLocked:!!t,handleInputChange:function(e,a){n(s=>({...s,[e]:a})),b[e]&&f(a=>{let s={...a};return delete s[e],s})},handleSubmit:S,setIsWeatherOverride:N,onCancel:i}}({cantiereId:e,certificazione:a,strumenti:s,preselectedCavoId:u,onSuccess:p,onCancel:h});return w?(0,t.jsx)("div",{className:"flex items-center justify-center h-96",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)(l.A,{className:"h-8 w-8 animate-spin mx-auto mb-4 text-blue-600"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Caricamento dati certificazione..."})]})}):(0,t.jsxs)("div",{className:"h-full w-full bg-gray-50",children:[(0,t.jsx)("div",{className:"bg-white border-b border-gray-200 sticky top-0 z-10",children:(0,t.jsx)("div",{className:"max-w-4xl mx-auto px-6 py-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg",children:(0,t.jsx)(n.A,{className:"h-6 w-6 text-blue-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-xl font-semibold text-gray-900",children:F?"Modifica Certificazione":"Nuova Certificazione CEI 64-8"}),u&&(0,t.jsxs)("p",{className:"text-sm text-gray-500",children:["Cavo: ",u]})]})]}),(0,t.jsx)(r.$,{variant:"ghost",size:"sm",onClick:h,className:"text-gray-400 hover:text-gray-600",children:(0,t.jsx)(o.A,{className:"h-5 w-5"})})]})})}),D&&(0,t.jsx)("div",{className:"bg-red-50 border-b border-red-200",children:(0,t.jsx)("div",{className:"max-w-4xl mx-auto px-6 py-4",children:(0,t.jsxs)(i.Fc,{variant:"destructive",className:"border-red-200 bg-red-50",children:[(0,t.jsx)(d.A,{className:"h-4 w-4"}),(0,t.jsx)(i.TN,{children:D})]})})}),(0,t.jsx)("div",{className:"max-w-4xl mx-auto px-6 py-8",children:(0,t.jsxs)("form",{onSubmit:e=>{e.preventDefault(),k()},className:"space-y-8",children:[(0,t.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden",children:[(0,t.jsxs)("div",{className:"px-6 py-4 border-b border-gray-100 bg-gray-50",children:[(0,t.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"\uD83D\uDCCB Informazioni Base"}),(0,t.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"Dati principali del cavo e operatore"})]}),(0,t.jsx)("div",{className:"p-6",children:(0,t.jsx)(v,{formData:g,cavi:j,responsabili:y,strumenti:s,validationErrors:O,isCavoLocked:A,onInputChange:I})})]}),(0,t.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden",children:[(0,t.jsxs)("div",{className:"px-6 py-4 border-b border-gray-100 bg-gray-50",children:[(0,t.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"\uD83C\uDF24️ Condizioni Ambientali"}),(0,t.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"Temperatura e umidit\xe0 durante la certificazione"})]}),(0,t.jsx)("div",{className:"p-6",children:(0,t.jsx)(b,{formData:g,weatherData:_,isLoadingWeather:z,isWeatherOverride:S,onInputChange:I,onToggleWeatherOverride:()=>R(!S)})})]}),(0,t.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden",children:[(0,t.jsxs)("div",{className:"px-6 py-4 border-b border-gray-100 bg-gray-50",children:[(0,t.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"⚡ Misurazioni e Test CEI 64-8"}),(0,t.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"Valori di isolamento, continuit\xe0 e parametri di prova"})]}),(0,t.jsx)("div",{className:"p-6",children:(0,t.jsx)(f,{formData:g,validationErrors:O,onInputChange:I})})]}),(0,t.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden",children:[(0,t.jsxs)("div",{className:"px-6 py-4 border-b border-gray-100 bg-gray-50",children:[(0,t.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"\uD83D\uDCDD Note e Stato Certificazione"}),(0,t.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"Osservazioni aggiuntive e stato finale"})]}),(0,t.jsx)("div",{className:"p-6",children:(0,t.jsx)(N,{formData:g,onInputChange:I})})]}),(0,t.jsx)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6",children:(0,t.jsxs)("div",{className:"flex justify-end gap-4",children:[(0,t.jsx)(r.$,{type:"button",variant:"outline",onClick:h,disabled:C,className:"px-8 py-2",children:"Annulla"}),(0,t.jsx)(r.$,{type:"submit",disabled:C,className:"px-8 py-2 bg-blue-600 hover:bg-blue-700",children:C?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(l.A,{className:"h-4 w-4 animate-spin mr-2"}),"Salvando..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(c.A,{className:"h-4 w-4 mr-2"}),F?"Aggiorna Certificazione":"Salva Certificazione"]})})]})})]})})]})}},96834:(e,a,s)=>{s.d(a,{E:()=>o});var t=s(60687);s(43210);var r=s(8730),i=s(24224),l=s(4780);let n=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function o({className:e,variant:a,asChild:s=!1,...i}){let o=s?r.DX:"span";return(0,t.jsx)(o,{"data-slot":"badge",className:(0,l.cn)(n({variant:a}),e),...i})}}};