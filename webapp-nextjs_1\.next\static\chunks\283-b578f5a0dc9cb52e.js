"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[283],{25731:(e,t,a)=>{a.d(t,{AR:()=>s,At:()=>c,CV:()=>l,Fw:()=>r,ZQ:()=>n,_I:()=>C,dG:()=>h,km:()=>p,kw:()=>d,l9:()=>u,mg:()=>m,om:()=>g,ug:()=>v});let o=a(23464).A.create({baseURL:"http://localhost:8001",timeout:3e4,headers:{"Content-Type":"application/json"}});o.interceptors.request.use(e=>{{let t=localStorage.getItem("token");t&&(e.headers.Authorization="Bearer ".concat(t))}return e},e=>Promise.reject(e)),o.interceptors.response.use(e=>e,e=>{var t;return(null==(t=e.response)?void 0:t.status)===401&&(localStorage.removeItem("token"),localStorage.removeItem("access_token"),localStorage.removeItem("user_data"),localStorage.removeItem("cantiere_data"),window.location.href="/login"),Promise.reject(e)});let i={get:async(e,t)=>(await o.get(e,t)).data,post:async(e,t,a)=>(await o.post(e,t,a)).data,put:async(e,t,a)=>(await o.put(e,t,a)).data,delete:async(e,t)=>(await o.delete(e,t)).data},n={login:async e=>{let t=new FormData;return t.append("username",e.username),t.append("password",e.password),(await o.post("/api/auth/login",t,{headers:{"Content-Type":"application/x-www-form-urlencoded"}})).data},loginCantiere:e=>i.post("/api/auth/login/cantiere",{codice_univoco:e.codice_cantiere,password:e.password_cantiere}),verifyToken:()=>i.post("/api/auth/test-token"),logout:()=>{localStorage.removeItem("access_token"),localStorage.removeItem("user_data"),window.location.href="/login"}},c={getCavi:(e,t)=>i.get("/api/cavi/".concat(e),{params:t}),getCavo:(e,t)=>i.get("/api/cavi/".concat(e,"/").concat(t)),checkCavo:(e,t)=>i.get("/api/cavi/".concat(e,"/check/").concat(t)),createCavo:(e,t)=>i.post("/api/cavi/".concat(e),t),updateCavo:(e,t,a)=>i.put("/api/cavi/".concat(e,"/").concat(t),a),deleteCavo:(e,t,a)=>i.delete("/api/cavi/".concat(e,"/").concat(t),{data:a}),updateMetriPosati:(e,t,a,o,n)=>i.post("/api/cavi/".concat(e,"/").concat(t,"/metri-posati"),{metri_posati:a,id_bobina:o,force_over:n||!1}),updateBobina:(e,t,a,o)=>i.post("/api/cavi/".concat(e,"/").concat(t,"/bobina"),{id_bobina:a,force_over:o||!1}),cancelInstallation:(e,t)=>i.post("/api/cavi/".concat(e,"/").concat(t,"/cancel-installation")),collegaCavo:(e,t,a,o)=>i.post("/api/cavi/".concat(e,"/").concat(t,"/collegamento"),{lato:a,responsabile:o}),scollegaCavo:(e,t,a)=>{let o={};return a&&(o.data={lato:a}),i.delete("/api/cavi/".concat(e,"/").concat(t,"/collegamento"),o)},markAsSpare:function(e,t,a){let o=!(arguments.length>3)||void 0===arguments[3]||arguments[3];return a?i.post("/api/cavi/".concat(e,"/").concat(t,"/mark-as-spare"),{force:o}):i.post("/api/cavi/".concat(e,"/").concat(t,"/reactivate-spare"),{})},debugCavi:e=>i.get("/api/cavi/debug/".concat(e)),debugCaviRaw:e=>i.get("/api/cavi/debug/raw/".concat(e))},r={getBobine:(e,t)=>i.get("/api/parco-cavi/".concat(e),{params:t}),getBobina:(e,t)=>i.get("/api/parco-cavi/".concat(e,"/").concat(t)),getBobineCompatibili:(e,t)=>i.get("/api/parco-cavi/".concat(e,"/compatibili"),{params:t}),createBobina:(e,t)=>i.post("/api/parco-cavi/".concat(e),t),updateBobina:(e,t,a)=>i.put("/api/parco-cavi/".concat(e,"/").concat(t),a),deleteBobina:(e,t)=>i.delete("/api/parco-cavi/".concat(e,"/").concat(t)),isFirstBobinaInsertion:e=>i.get("/api/parco-cavi/".concat(e,"/is-first-insertion")),updateBobina:(e,t,a)=>i.put("/api/parco-cavi/".concat(e,"/").concat(t),a),deleteBobina:(e,t)=>i.delete("/api/parco-cavi/".concat(e,"/").concat(t)),checkDisponibilita:(e,t,a)=>i.get("/api/parco-cavi/".concat(e,"/").concat(t,"/disponibilita"),{params:{metri_richiesti:a}})},l={getComande:e=>i.get("/api/comande/cantiere/".concat(e)),getComanda:(e,t)=>i.get("/api/comande/".concat(t)),getCaviComanda:e=>i.get("/api/comande/".concat(e,"/cavi")),createComanda:(e,t)=>i.post("/api/comande/cantiere/".concat(e),t),createComandaWithCavi:(e,t,a)=>i.post("/api/comande/cantiere/".concat(e,"/crea-con-cavi"),t,{params:{lista_id_cavi:a}}),updateDatiComanda:(e,t,a)=>i.put("/api/comande/".concat(e,"/").concat(t),a),updateComanda:(e,t,a)=>i.put("/api/comande/cantiere/".concat(e,"/").concat(t),a),deleteComanda:(e,t)=>i.delete("/api/comande/cantiere/".concat(e,"/").concat(t)),assegnaCavi:(e,t,a)=>i.post("/api/comande/cantiere/".concat(e,"/").concat(t,"/assegna-cavi"),{cavi_ids:a}),rimuoviCavi:(e,t,a)=>i.delete("/api/comande/cantiere/".concat(e,"/").concat(t,"/rimuovi-cavi"),{data:{cavi_ids:a}}),getStatistiche:e=>i.get("/api/comande/cantiere/".concat(e,"/statistiche")),cambiaStato:(e,t,a)=>i.put("/api/comande/cantiere/".concat(e,"/").concat(t,"/stato"),{nuovo_stato:a})},s={getResponsabili:e=>i.get("/api/responsabili/cantiere/".concat(e)),createResponsabile:(e,t)=>i.post("/api/responsabili/".concat(e),t),updateResponsabile:(e,t,a)=>i.put("/api/responsabili/".concat(e,"/").concat(t),a),deleteResponsabile:(e,t)=>i.delete("/api/responsabili/".concat(e,"/").concat(t))},p={getCertificazioni:(e,t)=>i.get("/api/cantieri/".concat(e,"/certificazioni"),{params:t?{filtro_cavo:t}:{}}),createCertificazione:(e,t)=>i.post("/api/cantieri/".concat(e,"/certificazioni"),t),getCertificazione:(e,t)=>i.get("/api/cantieri/".concat(e,"/certificazioni/").concat(t)),updateCertificazione:(e,t,a)=>i.put("/api/cantieri/".concat(e,"/certificazioni/").concat(t),a),deleteCertificazione:(e,t)=>i.delete("/api/cantieri/".concat(e,"/certificazioni/").concat(t)),generatePDF:(e,t)=>i.get("/api/cantieri/".concat(e,"/certificazioni/").concat(t,"/pdf"),{responseType:"blob"}),getStatistiche:e=>i.get("/api/cantieri/".concat(e,"/certificazioni/statistiche")),exportCertificazioni:(e,t)=>i.get("/api/cantieri/".concat(e,"/certificazioni/export"),{params:t,responseType:"blob"}),generateReport:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"completo";return i.get("/api/cantieri/".concat(e,"/certificazioni/report/").concat(t))},bulkDelete:(e,t)=>i.post("/api/cantieri/".concat(e,"/certificazioni/bulk-delete"),{ids:t}),generateBulkPdf:(e,t)=>i.post("/api/cantieri/".concat(e,"/certificazioni/bulk-pdf"),{ids:t},{responseType:"blob"}),validateCertificazione:(e,t)=>i.post("/api/cantieri/".concat(e,"/certificazioni/validate"),t)},d={getStrumenti:e=>i.get("/api/cantieri/".concat(e,"/strumenti")),createStrumento:(e,t)=>i.post("/api/cantieri/".concat(e,"/strumenti"),t),updateStrumento:(e,t,a)=>i.put("/api/cantieri/".concat(e,"/strumenti/").concat(t),a),deleteStrumento:(e,t)=>i.delete("/api/cantieri/".concat(e,"/strumenti/").concat(t))},u={getRapporti:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:100;return i.get("/api/cantieri/".concat(e,"/rapporti"),{params:{skip:t,limit:a}})},createRapporto:(e,t)=>i.post("/api/cantieri/".concat(e,"/rapporti"),t),getRapporto:(e,t)=>i.get("/api/cantieri/".concat(e,"/rapporti/").concat(t)),updateRapporto:(e,t,a)=>i.put("/api/cantieri/".concat(e,"/rapporti/").concat(t),a),deleteRapporto:(e,t)=>i.delete("/api/cantieri/".concat(e,"/rapporti/").concat(t)),aggiornaStatistiche:(e,t)=>i.post("/api/cantieri/".concat(e,"/rapporti/").concat(t,"/aggiorna-statistiche"))},g={getNonConformita:e=>i.get("/api/cantieri/".concat(e,"/non-conformita")),createNonConformita:(e,t)=>i.post("/api/cantieri/".concat(e,"/non-conformita"),t),updateNonConformita:(e,t,a)=>i.put("/api/cantieri/".concat(e,"/non-conformita/").concat(t),a),deleteNonConformita:(e,t)=>i.delete("/api/cantieri/".concat(e,"/non-conformita/").concat(t))},m={importCavi:(e,t,a)=>{let o=new FormData;return o.append("file",t),o.append("revisione",a),i.post("/api/excel/".concat(e,"/import-cavi"),o,{headers:{"Content-Type":"multipart/form-data"}})},importBobine:(e,t)=>{let a=new FormData;return a.append("file",t),i.post("/api/excel/".concat(e,"/import-parco-bobine"),a,{headers:{"Content-Type":"multipart/form-data"}})},exportCavi:e=>i.get("/api/excel/".concat(e,"/export-cavi"),{responseType:"blob"}),exportBobine:e=>i.get("/api/excel/".concat(e,"/export-parco-bobine"),{responseType:"blob"})},v={getReportAvanzamento:e=>i.get("/api/reports/".concat(e,"/avanzamento")),getReportBOQ:e=>i.get("/api/reports/".concat(e,"/boq")),getReportUtilizzoBobine:e=>i.get("/api/reports/".concat(e,"/storico-bobine")),getReportProgress:e=>i.get("/api/reports/".concat(e,"/progress")),getReportPosaPeriodo:(e,t,a)=>{let o=new URLSearchParams;t&&o.append("data_inizio",t),a&&o.append("data_fine",a);let n=o.toString();return i.get("/api/reports/".concat(e,"/posa-periodo").concat(n?"?".concat(n):""))}},C={getCantieri:()=>i.get("/api/cantieri"),getCantiere:e=>i.get("/api/cantieri/".concat(e)),createCantiere:e=>i.post("/api/cantieri",e),updateCantiere:(e,t)=>i.put("/api/cantieri/".concat(e),t),getCantiereStatistics:e=>i.get("/api/cantieri/".concat(e,"/statistics")),getWeatherData:e=>i.get("/api/cantieri/".concat(e,"/weather"))},h={getUsers:()=>i.get("/api/users"),getUser:e=>i.get("/api/users/".concat(e)),createUser:e=>i.post("/api/users",e),updateUser:(e,t)=>i.put("/api/users/".concat(e),t),deleteUser:e=>i.delete("/api/users/".concat(e)),toggleUserStatus:e=>i.get("/api/users/toggle/".concat(e)),checkExpiredUsers:()=>i.get("/api/users/check-expired"),impersonateUser:e=>i.post("/api/auth/impersonate",{user_id:e}),getDatabaseData:()=>i.get("/api/users/db-raw"),resetDatabase:()=>i.post("/api/admin/reset-database")}},40283:(e,t,a)=>{a.d(t,{A:()=>r,AuthProvider:()=>l});var o=a(95155),i=a(12115),n=a(25731);let c=(0,i.createContext)(void 0);function r(){let e=(0,i.useContext)(c);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}function l(e){let{children:t}=e,[a,r]=(0,i.useState)(null),[l,s]=(0,i.useState)(null),[p,d]=(0,i.useState)(!0),[u,g]=(0,i.useState)(()=>"true"===localStorage.getItem("isImpersonating")),[m,v]=(0,i.useState)(()=>{{let e=localStorage.getItem("impersonatedUser");return e?JSON.parse(e):null}}),[C,h]=(0,i.useState)(null),[_,S]=(0,i.useState)(null),[f,I]=(0,i.useState)(null),b=!!a&&a.id_utente||!!l&&l.id_cantiere;(0,i.useEffect)(()=>{console.log("\uD83D\uDD10 AuthContext: Inizializzazione - controllo autenticazione esistente"),localStorage.getItem("token")?(console.log("\uD83D\uDD10 AuthContext: Token trovato, verifica validit\xe0"),x()):(console.log("\uD83D\uDD10 AuthContext: Nessun token trovato, richiesto login"),d(!1),r(null),s(null),v(null),g(!1))},[]),(0,i.useEffect)(()=>{if(a&&!p&&!l){let e=localStorage.getItem("selectedCantiereId"),t=localStorage.getItem("selectedCantiereName");if(e&&"null"!==e&&"undefined"!==e){let o=parseInt(e,10);!isNaN(o)&&o>0?s({id_cantiere:o,commessa:t||"Cantiere ".concat(o),codice_univoco:"",id_utente:a.id_utente}):(localStorage.removeItem("selectedCantiereId"),localStorage.removeItem("selectedCantiereName"))}}},[a,p,l]);let x=async()=>{try{if(d(!0),localStorage.getItem("token"))try{let e=await n.ZQ.verifyToken(),t={id_utente:e.user_id,username:e.username,ruolo:e.role};r(t);let a=!0===e.is_impersonated;if(g(a),a&&e.impersonated_id){let t={id:e.impersonated_id,username:e.impersonated_username,role:e.impersonated_role};v(t),localStorage.setItem("impersonatedUser",JSON.stringify(t)),localStorage.setItem("isImpersonating","true")}else v(null),localStorage.removeItem("impersonatedUser"),localStorage.removeItem("isImpersonating");if("cantieri_user"===e.role&&e.cantiere_id){let t={id_cantiere:e.cantiere_id,commessa:e.cantiere_name||"Cantiere ".concat(e.cantiere_id),codice_univoco:"",id_utente:e.user_id};console.log("\uD83C\uDFD7️ AuthContext: Impostazione cantiere per utente cantiere:",t),s(t),localStorage.setItem("selectedCantiereId",e.cantiere_id.toString()),localStorage.setItem("selectedCantiereName",t.commessa)}else{console.log("\uD83C\uDFD7️ AuthContext: Utente standard, controllo cantiere dal localStorage");let e=localStorage.getItem("cantiere_data");if(e)try{let t=JSON.parse(e);console.log("\uD83C\uDFD7️ AuthContext: Caricamento cantiere da cantiere_data:",t),s(t),localStorage.setItem("selectedCantiereId",t.id_cantiere.toString()),localStorage.setItem("selectedCantiereName",t.commessa)}catch(e){console.warn("\uD83C\uDFD7️ AuthContext: Errore parsing cantiere_data:",e),localStorage.removeItem("cantiere_data")}}}catch(e){console.error("\uD83D\uDD10 AuthContext: Token non valido:",e),localStorage.removeItem("token"),localStorage.removeItem("access_token"),localStorage.removeItem("user_data"),localStorage.removeItem("cantiere_data"),localStorage.removeItem("selectedCantiereId"),localStorage.removeItem("selectedCantiereName"),localStorage.removeItem("isImpersonating"),localStorage.removeItem("impersonatedUser"),r(null),s(null),g(!1),v(null)}else console.log("\uD83D\uDD10 AuthContext: Nessun token trovato"),r(null),s(null)}catch(e){console.error("\uD83D\uDD10 AuthContext: Errore generale durante checkAuth:",e),localStorage.removeItem("token"),localStorage.removeItem("access_token"),localStorage.removeItem("user_data"),localStorage.removeItem("cantiere_data"),localStorage.removeItem("isImpersonating"),localStorage.removeItem("impersonatedUser"),r(null),s(null),g(!1),v(null)}finally{console.log("\uD83D\uDD10 AuthContext: checkAuth completato, impostazione loading = false"),d(!1)}},k=async(e,t)=>{try{console.log("\uD83D\uDD10 AuthContext: Inizio login per:",e),d(!0);let a=await n.ZQ.login({username:e,password:t});console.log("\uD83D\uDCE1 AuthContext: Risposta backend ricevuta:",a);{localStorage.setItem("token",a.access_token),console.log("\uD83D\uDCBE AuthContext: Token salvato nel localStorage");let e={id_utente:a.user_id,username:a.username,ruolo:a.role};return a.expiration_warning?(console.log("⚠️ AuthContext: Warning scadenza ricevuto:",a.expiration_warning),h(a.expiration_warning),S(a.days_until_expiration),I(a.expiration_date)):(h(null),S(null),I(null)),console.log("\uD83D\uDC64 AuthContext: Dati utente creati:",e),r(e),s(null),console.log("✅ AuthContext: Stato utente aggiornato, restituisco userData"),e}}catch(e){throw console.error("❌ AuthContext: Errore durante login:",e),e}finally{d(!1)}},w=async(e,t)=>{try{console.log("\uD83D\uDD10 AuthContext: Inizio login cantiere:",e),d(!0);let a=await n.ZQ.loginCantiere({codice_cantiere:e,password_cantiere:t});console.log("\uD83D\uDD10 AuthContext: Risposta login cantiere:",a);{localStorage.setItem("token",a.access_token),console.log("\uD83D\uDD10 AuthContext: Token salvato");let t={id_cantiere:a.cantiere_id,commessa:a.cantiere_name,codice_univoco:e,id_utente:a.user_id};return console.log("\uD83D\uDD10 AuthContext: Dati cantiere preparati:",t),localStorage.setItem("cantiere_data",JSON.stringify(t)),console.log("\uD83D\uDD10 AuthContext: Dati cantiere salvati in localStorage"),s(t),r(null),console.log("\uD83D\uDD10 AuthContext: Context aggiornato"),await x(),console.log("\uD83D\uDD10 AuthContext: checkAuth completato"),t}}catch(e){throw e}finally{d(!1)}},A=async e=>{try{let t=await n.dG.impersonateUser(e);{localStorage.setItem("token",t.access_token);let e={id:t.impersonated_id,username:t.impersonated_username,role:t.impersonated_role};return localStorage.setItem("impersonatedUser",JSON.stringify(e)),v(e),g(!0),localStorage.setItem("isImpersonating","true"),{impersonatedUser:e}}}catch(e){throw e}};return(0,o.jsx)(c.Provider,{value:{user:a,cantiere:l,isAuthenticated:b,isLoading:p,isImpersonating:u,impersonatedUser:m,expirationWarning:C,daysUntilExpiration:_,expirationDate:f,login:k,loginCantiere:w,logout:()=>{localStorage.clear(),sessionStorage.clear(),r(null),s(null),g(!1),v(null),h(null),S(null),I(null),window.location.replace("/login")},checkAuth:x,impersonateUser:A,selectCantiere:e=>{if(!e||!e.id_cantiere||e.id_cantiere<=0)return void console.error("\uD83C\uDFD7️ AuthContext: Tentativo di selezione cantiere non valido:",e);try{let t=e.commessa||"Cantiere ".concat(e.id_cantiere);localStorage.setItem("selectedCantiereId",e.id_cantiere.toString()),localStorage.setItem("selectedCantiereName",t);let a={...e,commessa:t};console.log("\uD83C\uDFD7️ AuthContext: Cantiere selezionato:",a),s(a),localStorage.removeItem("cantiere_data")}catch(e){console.error("\uD83C\uDFD7️ AuthContext: Errore nella selezione cantiere:",e)}},clearCantiere:()=>{console.log("\uD83C\uDFD7️ AuthContext: Pulizia stato cantiere"),s(null),localStorage.removeItem("selectedCantiereId"),localStorage.removeItem("selectedCantiereName"),localStorage.removeItem("cantiere_data")},dismissExpirationWarning:()=>{h(null),S(null),I(null)}},children:t})}}}]);