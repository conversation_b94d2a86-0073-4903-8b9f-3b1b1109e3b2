module.exports = "\"use strict\";var s=Object.defineProperty,p=Object.getOwnPropertyDescriptor,_=Object.getOwnPropertyNames,u=Object.prototype.hasOwnProperty,a=(t,e)=>s(t,\"name\",{value:e,configurable:!0}),h=(t,e)=>{for(var r in e)s(t,r,{get:e[r],enumerable:!0})},l=(t,e,r,o)=>{if(e&&typeof e==\"object\"||typeof e==\"function\")for(let n of _(e))!u.call(t,n)&&n!==r&&s(t,n,{get:()=>e[n],enumerable:!(o=p(e,n))||o.enumerable});return t},P=t=>l(s({},\"__esModule\",{value:!0}),t),i={};h(i,{FetchEvent:()=>E,PromiseRejectionEvent:()=>j});module.exports=P(i);var c=class extends Event{constructor(e){super(\"fetch\"),this.request=e,this.response=null,this.awaiting=new Set}respondWith=e=>{this.response=e};waitUntil=e=>{this.awaiting.add(e),e.finally(()=>this.awaiting.delete(e))}};a(c,\"FetchEvent\");var E=c,v=class extends Event{constructor(e,r){super(e,{cancelable:!0}),this.promise=r.promise,this.reason=r.reason}};a(v,\"PromiseRejectionEvent\");var j=v;\n"