(()=>{var e={};e.id=483,e.ids=[483],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},40552:(e,t,r)=>{Promise.resolve().then(r.bind(r,65697))},53704:(e,t,r)=>{Promise.resolve().then(r.bind(r,66103))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65697:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(60687),o=r(43210);function n(){let[e,t]=(0,o.useState)(""),[r,n]=(0,o.useState)(!1),i=async()=>{n(!0),t("Testing...");try{console.log("Starting admin login test...");let e=new FormData;e.append("username","admin"),e.append("password","admin"),console.log("Sending request to:","http://localhost:8001/api/auth/login");let r=await fetch("http://localhost:8001/api/auth/login",{method:"POST",body:e});console.log("Response status:",r.status),console.log("Response headers:",r.headers);let s=await r.json();console.log("Response data:",s),r.ok?t(`SUCCESS: ${JSON.stringify(s,null,2)}`):t(`ERROR: ${r.status} - ${JSON.stringify(s,null,2)}`)}catch(e){console.error("Login test error:",e),t(`EXCEPTION: ${e.message}`)}finally{n(!1)}},a=async()=>{n(!0),t("Testing...");try{console.log("Starting cantiere login test...");let e=await fetch("http://localhost:8001/api/auth/login/cantiere",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({codice_univoco:"TEST123",password:"test123"})});console.log("Response status:",e.status);let r=await e.json();console.log("Response data:",r),e.ok?t(`SUCCESS: ${JSON.stringify(r,null,2)}`):t(`ERROR: ${e.status} - ${JSON.stringify(r,null,2)}`)}catch(e){console.error("Cantiere login test error:",e),t(`EXCEPTION: ${e.message}`)}finally{n(!1)}};return(0,s.jsxs)("div",{style:{padding:"20px",fontFamily:"monospace"},children:[(0,s.jsx)("h1",{children:"Test Login Page"}),(0,s.jsxs)("div",{style:{marginBottom:"20px"},children:[(0,s.jsx)("button",{onClick:i,disabled:r,style:{padding:"10px 20px",marginRight:"10px",backgroundColor:"#007bff",color:"white",border:"none",borderRadius:"5px",cursor:r?"not-allowed":"pointer"},children:r?"Testing...":"Test Admin Login"}),(0,s.jsx)("button",{onClick:a,disabled:r,style:{padding:"10px 20px",backgroundColor:"#28a745",color:"white",border:"none",borderRadius:"5px",cursor:r?"not-allowed":"pointer"},children:r?"Testing...":"Test Cantiere Login"})]}),(0,s.jsx)("div",{style:{backgroundColor:"#f8f9fa",padding:"15px",borderRadius:"5px",whiteSpace:"pre-wrap",minHeight:"200px"},children:e||"Click a button to test login..."})]})}},66103:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\test-login\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\CMS\\webapp-nextjs\\src\\app\\test-login\\page.tsx","default")},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var s=r(31658);let o=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")},97652:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>c,tree:()=>p});var s=r(65239),o=r(48088),n=r(88170),i=r.n(n),a=r(30893),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);r.d(t,l);let p={children:["",{children:["test-login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,66103)),"C:\\CMS\\webapp-nextjs\\src\\app\\test-login\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\CMS\\webapp-nextjs\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\CMS\\webapp-nextjs\\src\\app\\test-login\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},c=new s.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/test-login/page",pathname:"/test-login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,991,658,639],()=>r(97652));module.exports=s})();