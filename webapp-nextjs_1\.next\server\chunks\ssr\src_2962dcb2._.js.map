{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Tabs = TabsPrimitive.Root\n\nconst TabsList = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.List>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.List\n    ref={ref}\n    className={cn(\n      \"inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsList.displayName = TabsPrimitive.List.displayName\n\nconst TabsTrigger = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName\n\nconst TabsContent = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Content\n    ref={ref}\n    className={cn(\n      \"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsContent.displayName = TabsPrimitive.Content.displayName\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,OAAO,gKAAA,CAAA,OAAkB;AAE/B,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,OAAkB;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAED,GAAG,KAAK;;;;;;AAGb,SAAS,WAAW,GAAG,gKAAA,CAAA,OAAkB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uYACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,gKAAA,CAAA,UAAqB,CAAC,WAAW;AAE3D,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mIACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,gKAAA,CAAA,UAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 157, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/hooks/useCantiere.ts"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { Cantiere } from '@/types'\n\ninterface UseCantiereResult {\n  cantiereId: number | null\n  cantiere: Cantiere | null\n  isValidCantiere: boolean\n  isLoading: boolean\n  error: string | null\n  validateCantiere: (id: number | string) => boolean\n  clearError: () => void\n}\n\n/**\n * Hook personalizzato per la gestione robusta del cantiere selezionato\n * Gestisce validazione, errori e sincronizzazione con AuthContext\n */\nexport function useCantiere(): UseCantiereResult {\n  const { cantiere, isLoading: authLoading } = useAuth()\n  const [cantiereId, setCantiereId] = useState<number | null>(null)\n  const [isLoading, setIsLoading] = useState(true)\n  const [error, setError] = useState<string | null>(null)\n\n  // Funzione per validare un ID cantiere\n  const validateCantiere = (id: number | string): boolean => {\n    if (id === null || id === undefined) return false\n    \n    const numId = typeof id === 'string' ? parseInt(id, 10) : id\n    \n    if (isNaN(numId) || numId <= 0) {\n      console.warn('🏗️ useCantiere: ID cantiere non valido:', id)\n      return false\n    }\n    \n    return true\n  }\n\n  // Effetto per sincronizzare con AuthContext e localStorage\n  useEffect(() => {\n    if (authLoading) {\n      console.log('🏗️ useCantiere: Autenticazione in corso...')\n      return\n    }\n\n    setIsLoading(true)\n    setError(null)\n\n    try {\n      let selectedId: number | null = null\n\n      // Priorità 1: Cantiere dal context di autenticazione (login cantiere diretto)\n      if (cantiere?.id_cantiere && validateCantiere(cantiere.id_cantiere)) {\n        selectedId = cantiere.id_cantiere\n        console.log('🏗️ useCantiere: Usando cantiere dal context (login cantiere):', selectedId)\n      } else {\n        // Priorità 2: Cantiere dal localStorage (cantiere_data per login cantiere)\n        const cantiereData = localStorage.getItem('cantiere_data')\n        if (cantiereData) {\n          try {\n            const parsedData = JSON.parse(cantiereData)\n            if (parsedData.id_cantiere && validateCantiere(parsedData.id_cantiere)) {\n              selectedId = parsedData.id_cantiere\n              console.log('🏗️ useCantiere: Usando cantiere da cantiere_data:', selectedId)\n            }\n          } catch (parseError) {\n            console.warn('🏗️ useCantiere: Errore parsing cantiere_data:', parseError)\n          }\n        }\n\n        // Priorità 3: Cantiere dal localStorage (selectedCantiereId per selezione manuale)\n        if (!selectedId) {\n          const storedId = localStorage.getItem('selectedCantiereId')\n          if (storedId && validateCantiere(storedId)) {\n            selectedId = parseInt(storedId, 10)\n            console.log('🏗️ useCantiere: Usando cantiere da selectedCantiereId:', selectedId)\n          }\n        }\n      }\n\n      if (selectedId) {\n        setCantiereId(selectedId)\n        console.log('🏗️ useCantiere: Cantiere valido impostato:', selectedId)\n      } else {\n        console.warn('🏗️ useCantiere: Nessun cantiere valido trovato')\n        setCantiereId(null)\n        setError('Nessun cantiere selezionato. Seleziona un cantiere per continuare.')\n      }\n    } catch (err) {\n      console.error('🏗️ useCantiere: Errore nella gestione cantiere:', err)\n      setError('Errore nella gestione del cantiere selezionato.')\n      setCantiereId(null)\n    } finally {\n      setIsLoading(false)\n    }\n  }, [cantiere, authLoading])\n\n  const clearError = () => setError(null)\n\n  return {\n    cantiereId,\n    cantiere,\n    isValidCantiere: cantiereId !== null && cantiereId > 0,\n    isLoading,\n    error,\n    validateCantiere,\n    clearError\n  }\n}\n\n/**\n * Hook semplificato che restituisce solo l'ID del cantiere valido o null\n */\nexport function useCantiereId(): number | null {\n  const { cantiereId } = useCantiere()\n  return cantiereId\n}\n\n/**\n * Hook che forza la presenza di un cantiere valido\n * Lancia un errore se non c'è un cantiere selezionato\n */\nexport function useRequiredCantiere(): { cantiereId: number; cantiere: Cantiere | null } {\n  const { cantiereId, cantiere, isLoading, error } = useCantiere()\n\n  if (isLoading) {\n    throw new Error('Caricamento cantiere in corso...')\n  }\n\n  if (error) {\n    throw new Error(error)\n  }\n\n  if (!cantiereId || cantiereId <= 0) {\n    throw new Error('Nessun cantiere selezionato. Seleziona un cantiere per continuare.')\n  }\n\n  return { cantiereId, cantiere }\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;AAoBO,SAAS;IACd,MAAM,EAAE,QAAQ,EAAE,WAAW,WAAW,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACnD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC5D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,uCAAuC;IACvC,MAAM,mBAAmB,CAAC;QACxB,IAAI,OAAO,QAAQ,OAAO,WAAW,OAAO;QAE5C,MAAM,QAAQ,OAAO,OAAO,WAAW,SAAS,IAAI,MAAM;QAE1D,IAAI,MAAM,UAAU,SAAS,GAAG;YAC9B,QAAQ,IAAI,CAAC,4CAA4C;YACzD,OAAO;QACT;QAEA,OAAO;IACT;IAEA,2DAA2D;IAC3D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,aAAa;YACf,QAAQ,GAAG,CAAC;YACZ;QACF;QAEA,aAAa;QACb,SAAS;QAET,IAAI;YACF,IAAI,aAA4B;YAEhC,8EAA8E;YAC9E,IAAI,UAAU,eAAe,iBAAiB,SAAS,WAAW,GAAG;gBACnE,aAAa,SAAS,WAAW;gBACjC,QAAQ,GAAG,CAAC,kEAAkE;YAChF,OAAO;gBACL,2EAA2E;gBAC3E,MAAM,eAAe,aAAa,OAAO,CAAC;gBAC1C,IAAI,cAAc;oBAChB,IAAI;wBACF,MAAM,aAAa,KAAK,KAAK,CAAC;wBAC9B,IAAI,WAAW,WAAW,IAAI,iBAAiB,WAAW,WAAW,GAAG;4BACtE,aAAa,WAAW,WAAW;4BACnC,QAAQ,GAAG,CAAC,sDAAsD;wBACpE;oBACF,EAAE,OAAO,YAAY;wBACnB,QAAQ,IAAI,CAAC,kDAAkD;oBACjE;gBACF;gBAEA,mFAAmF;gBACnF,IAAI,CAAC,YAAY;oBACf,MAAM,WAAW,aAAa,OAAO,CAAC;oBACtC,IAAI,YAAY,iBAAiB,WAAW;wBAC1C,aAAa,SAAS,UAAU;wBAChC,QAAQ,GAAG,CAAC,2DAA2D;oBACzE;gBACF;YACF;YAEA,IAAI,YAAY;gBACd,cAAc;gBACd,QAAQ,GAAG,CAAC,+CAA+C;YAC7D,OAAO;gBACL,QAAQ,IAAI,CAAC;gBACb,cAAc;gBACd,SAAS;YACX;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,oDAAoD;YAClE,SAAS;YACT,cAAc;QAChB,SAAU;YACR,aAAa;QACf;IACF,GAAG;QAAC;QAAU;KAAY;IAE1B,MAAM,aAAa,IAAM,SAAS;IAElC,OAAO;QACL;QACA;QACA,iBAAiB,eAAe,QAAQ,aAAa;QACrD;QACA;QACA;QACA;IACF;AACF;AAKO,SAAS;IACd,MAAM,EAAE,UAAU,EAAE,GAAG;IACvB,OAAO;AACT;AAMO,SAAS;IACd,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG;IAEnD,IAAI,WAAW;QACb,MAAM,IAAI,MAAM;IAClB;IAEA,IAAI,OAAO;QACT,MAAM,IAAI,MAAM;IAClB;IAEA,IAAI,CAAC,cAAc,cAAc,GAAG;QAClC,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;QAAE;QAAY;IAAS;AAChC", "debugId": null}}, {"offset": {"line": 275, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/cantiere/CantiereErrorBoundary.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { Button } from '@/components/ui/button'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { AlertCircle, Construction, RefreshCw, ArrowLeft } from 'lucide-react'\nimport { useRouter } from 'next/navigation'\nimport { useCantiere } from '@/hooks/useCantiere'\n\ninterface CantiereErrorBoundaryProps {\n  children: React.ReactNode\n  fallback?: React.ReactNode\n  showBackButton?: boolean\n  backUrl?: string\n}\n\n/**\n * Componente per gestire errori relativi alla selezione del cantiere\n * Mostra messaggi di errore appropriati e opzioni di recupero\n */\nexport function CantiereErrorBoundary({ \n  children, \n  fallback, \n  showBackButton = true, \n  backUrl = '/cantieri' \n}: CantiereErrorBoundaryProps) {\n  const router = useRouter()\n  const { cantiereId, cantiere, isValidCantiere, isLoading, error, clearError } = useCantiere()\n\n  // Se stiamo caricando, mostra un loader\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6\">\n        <div className=\"max-w-4xl mx-auto\">\n          <Card>\n            <CardContent className=\"flex items-center justify-center p-8\">\n              <div className=\"text-center\">\n                <RefreshCw className=\"h-8 w-8 animate-spin mx-auto mb-4 text-blue-600\" />\n                <p className=\"text-lg font-medium text-gray-700\">Caricamento cantiere...</p>\n                <p className=\"text-sm text-gray-500 mt-2\">Verifica della selezione cantiere in corso</p>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      </div>\n    )\n  }\n\n  // Se c'è un errore o il cantiere non è valido, mostra l'errore\n  if (error || !isValidCantiere) {\n    const errorMessage = error || 'Nessun cantiere selezionato'\n    \n    if (fallback) {\n      return <>{fallback}</>\n    }\n\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6\">\n        <div className=\"max-w-4xl mx-auto space-y-6\">\n          \n          {/* Header con errore */}\n          <Card className=\"border-red-200 bg-red-50\">\n            <CardHeader>\n              <CardTitle className=\"flex items-center text-red-800\">\n                <AlertCircle className=\"h-6 w-6 mr-2\" />\n                Problema con la selezione del cantiere\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <Alert variant=\"destructive\">\n                <AlertCircle className=\"h-4 w-4\" />\n                <AlertDescription>\n                  <strong>Errore:</strong> {errorMessage}\n                </AlertDescription>\n              </Alert>\n            </CardContent>\n          </Card>\n\n          {/* Informazioni di debug */}\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center text-gray-700\">\n                <Construction className=\"h-5 w-5 mr-2\" />\n                Informazioni cantiere\n              </CardTitle>\n            </CardHeader>\n            <CardContent className=\"space-y-3\">\n              <div className=\"grid grid-cols-2 gap-4 text-sm\">\n                <div>\n                  <span className=\"font-medium text-gray-600\">ID Cantiere:</span>\n                  <span className=\"ml-2 text-gray-800\">{cantiereId || 'Non disponibile'}</span>\n                </div>\n                <div>\n                  <span className=\"font-medium text-gray-600\">Nome Cantiere:</span>\n                  <span className=\"ml-2 text-gray-800\">{cantiere?.commessa || 'Non disponibile'}</span>\n                </div>\n                <div>\n                  <span className=\"font-medium text-gray-600\">Cantiere Valido:</span>\n                  <span className={`ml-2 ${isValidCantiere ? 'text-green-600' : 'text-red-600'}`}>\n                    {isValidCantiere ? 'Sì' : 'No'}\n                  </span>\n                </div>\n                <div>\n                  <span className=\"font-medium text-gray-600\">localStorage ID:</span>\n                  <span className=\"ml-2 text-gray-800\">\n                    {typeof window !== 'undefined' ? localStorage.getItem('selectedCantiereId') || 'Non presente' : 'N/A'}\n                  </span>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* Azioni di recupero */}\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"text-gray-700\">Azioni disponibili</CardTitle>\n            </CardHeader>\n            <CardContent className=\"space-y-3\">\n              <div className=\"flex flex-wrap gap-3\">\n                \n                {/* Pulsante per tornare ai cantieri */}\n                {showBackButton && (\n                  <Button \n                    onClick={() => router.push(backUrl)}\n                    variant=\"default\"\n                    className=\"flex items-center\"\n                  >\n                    <ArrowLeft className=\"h-4 w-4 mr-2\" />\n                    Seleziona Cantiere\n                  </Button>\n                )}\n\n                {/* Pulsante per pulire errore */}\n                {error && (\n                  <Button \n                    onClick={clearError}\n                    variant=\"outline\"\n                    className=\"flex items-center\"\n                  >\n                    <RefreshCw className=\"h-4 w-4 mr-2\" />\n                    Riprova\n                  </Button>\n                )}\n\n                {/* Pulsante per pulire localStorage */}\n                <Button \n                  onClick={() => {\n                    localStorage.removeItem('selectedCantiereId')\n                    localStorage.removeItem('selectedCantiereName')\n                    localStorage.removeItem('cantiere_data')\n                    window.location.reload()\n                  }}\n                  variant=\"outline\"\n                  className=\"flex items-center text-orange-600 border-orange-300 hover:bg-orange-50\"\n                >\n                  <AlertCircle className=\"h-4 w-4 mr-2\" />\n                  Reset Dati Cantiere\n                </Button>\n              </div>\n\n              <div className=\"mt-4 p-3 bg-blue-50 rounded-lg\">\n                <p className=\"text-sm text-blue-800\">\n                  <strong>Suggerimento:</strong> Se il problema persiste, prova a selezionare nuovamente un cantiere \n                  dalla pagina principale o contatta l'amministratore del sistema.\n                </p>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      </div>\n    )\n  }\n\n  // Se tutto è ok, renderizza i children\n  return <>{children}</>\n}\n\n/**\n * Hook per utilizzare il CantiereErrorBoundary in modo condizionale\n */\nexport function useCantiereErrorBoundary() {\n  const { isValidCantiere, isLoading, error } = useCantiere()\n  \n  return {\n    shouldShowError: !isLoading && (!isValidCantiere || error),\n    isLoading,\n    error\n  }\n}\n"], "names": [], "mappings": ";;;;;AAGA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AARA;;;;;;;;AAqBO,SAAS,sBAAsB,EACpC,QAAQ,EACR,QAAQ,EACR,iBAAiB,IAAI,EACrB,UAAU,WAAW,EACM;IAC3B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,eAAe,EAAE,SAAS,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,cAAW,AAAD;IAE1F,wCAAwC;IACxC,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;8BACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;kCACrB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;8CACrB,8OAAC;oCAAE,WAAU;8CAAoC;;;;;;8CACjD,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAOxD;IAEA,+DAA+D;IAC/D,IAAI,SAAS,CAAC,iBAAiB;QAC7B,MAAM,eAAe,SAAS;QAE9B,IAAI,UAAU;YACZ,qBAAO;0BAAG;;QACZ;QAEA,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAGb,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,8OAAC,gIAAA,CAAA,aAAU;0CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,8OAAC,oNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;0CAI5C,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;;sDACb,8OAAC,oNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;sDACvB,8OAAC,iIAAA,CAAA,mBAAgB;;8DACf,8OAAC;8DAAO;;;;;;gDAAgB;gDAAE;;;;;;;;;;;;;;;;;;;;;;;;kCAOlC,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;0CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,8OAAC,kNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;0CAI7C,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAK,WAAU;8DAA4B;;;;;;8DAC5C,8OAAC;oDAAK,WAAU;8DAAsB,cAAc;;;;;;;;;;;;sDAEtD,8OAAC;;8DACC,8OAAC;oDAAK,WAAU;8DAA4B;;;;;;8DAC5C,8OAAC;oDAAK,WAAU;8DAAsB,UAAU,YAAY;;;;;;;;;;;;sDAE9D,8OAAC;;8DACC,8OAAC;oDAAK,WAAU;8DAA4B;;;;;;8DAC5C,8OAAC;oDAAK,WAAW,CAAC,KAAK,EAAE,kBAAkB,mBAAmB,gBAAgB;8DAC3E,kBAAkB,OAAO;;;;;;;;;;;;sDAG9B,8OAAC;;8DACC,8OAAC;oDAAK,WAAU;8DAA4B;;;;;;8DAC5C,8OAAC;oDAAK,WAAU;8DACb,6EAA+F;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQ1G,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;0CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;8CAAgB;;;;;;;;;;;0CAEvC,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC;wCAAI,WAAU;;4CAGZ,gCACC,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAS,IAAM,OAAO,IAAI,CAAC;gDAC3B,SAAQ;gDACR,WAAU;;kEAEV,8OAAC,gNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;4CAMzC,uBACC,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAS;gDACT,SAAQ;gDACR,WAAU;;kEAEV,8OAAC,gNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAM1C,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAS;oDACP,aAAa,UAAU,CAAC;oDACxB,aAAa,UAAU,CAAC;oDACxB,aAAa,UAAU,CAAC;oDACxB,OAAO,QAAQ,CAAC,MAAM;gDACxB;gDACA,SAAQ;gDACR,WAAU;;kEAEV,8OAAC,oNAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;kDAK5C,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAE,WAAU;;8DACX,8OAAC;8DAAO;;;;;;gDAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAS9C;IAEA,uCAAuC;IACvC,qBAAO;kBAAG;;AACZ;AAKO,SAAS;IACd,MAAM,EAAE,eAAe,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,cAAW,AAAD;IAExD,OAAO;QACL,iBAAiB,CAAC,aAAa,CAAC,CAAC,mBAAmB,KAAK;QACzD;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 746, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/app/reports/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport '@/styles/admin.css'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Badge } from '@/components/ui/badge'\nimport { Progress } from '@/components/ui/progress'\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { useCantiere } from '@/hooks/useCantiere'\nimport { CantiereErrorBoundary } from '@/components/cantiere/CantiereErrorBoundary'\nimport { reportsApi, cantieriApi } from '@/lib/api'\nimport { ReportAvanzamento, ReportBOQ, Cantiere } from '@/types'\nimport {\n  BarChart3,\n  Download,\n  Calendar,\n  TrendingUp,\n  Target,\n  Activity,\n  Clock,\n  CheckCircle,\n  Loader2,\n  AlertCircle,\n  AlertTriangle,\n  FileText,\n  Package,\n  Users,\n  Zap,\n  RefreshCw\n} from 'lucide-react'\nimport {\n  BarChart,\n  Bar,\n  XAxis,\n  YAxis,\n  CartesianGrid,\n  Tooltip,\n  ResponsiveContainer,\n  PieChart,\n  Pie,\n  Cell,\n  LineChart,\n  Line,\n  Area,\n  AreaChart\n} from 'recharts'\n\nexport default function ReportsPage() {\n  const [activeTab, setActiveTab] = useState('avanzamento')\n  const [selectedPeriod, setSelectedPeriod] = useState('month')\n  const [reportAvanzamento, setReportAvanzamento] = useState<any>(null)\n  const [reportBOQ, setReportBOQ] = useState<any>(null)\n  const [reportUtilizzoBobine, setReportUtilizzoBobine] = useState<any>(null)\n  const [reportProgress, setReportProgress] = useState<any>(null)\n  const [isLoading, setIsLoading] = useState(true)\n  const [error, setError] = useState('')\n\n  const { user, isLoading: authLoading } = useAuth()\n  const { cantiereId, cantiere, isValidCantiere, isLoading: cantiereLoading, error: cantiereError } = useCantiere()\n\n  // Load all basic reports on component mount - MIGLIORATO per evitare race conditions\n  useEffect(() => {\n\n    const loadAllReports = async () => {\n      setIsLoading(true)\n      try {\n        // Usa il cantiere dal nuovo hook per gestione robusta\n        const currentCantiereId = cantiereId\n\n        // Test del token\n        const token = localStorage.getItem('token')\n        console.log('🏗️ ReportsPage: Caricamento report per cantiere:', currentCantiereId)\n\n        if (!currentCantiereId || currentCantiereId <= 0) {\n          console.warn('🏗️ ReportsPage: Nessun cantiere valido selezionato')\n          setError(cantiereError || 'Nessun cantiere selezionato. Seleziona un cantiere da \"Gestisci Cantieri\".')\n          setIsLoading(false)\n          return\n        }\n\n        // Create individual promises that handle their own errors - come nella webapp originale\n        // Aggiungiamo il parametro formato=video per ottenere i dati invece dei file\n\n        // Helper function per timeout - aumentato timeout per API lente\n        const fetchWithTimeout = (url: string, options: any, timeout = 30000) => {\n          return Promise.race([\n            fetch(url, options),\n            new Promise((_, reject) =>\n              setTimeout(() => reject(new Error(`Timeout dopo ${timeout/1000}s per ${url}`)), timeout)\n            )\n          ]) as Promise<Response>\n        }\n\n        const progressPromise = fetchWithTimeout(`http://localhost:8001/api/reports/${currentCantiereId}/progress?formato=video`, {\n          headers: {\n            'Authorization': `Bearer ${localStorage.getItem('token')}`,\n            'Content-Type': 'application/json'\n          }\n        })\n          .then(res => {\n            return res.json()\n          })\n          .then(data => {\n            return data\n          })\n          .catch(err => {\n            return { content: null, error: err.message.includes('Timeout') ? 'Timeout API Progress' : 'Errore API Progress' }\n          })\n\n        const boqPromise = fetchWithTimeout(`http://localhost:8001/api/reports/${currentCantiereId}/boq?formato=video`, {\n          headers: {\n            'Authorization': `Bearer ${localStorage.getItem('token')}`,\n            'Content-Type': 'application/json'\n          }\n        })\n          .then(res => {\n            return res.json()\n          })\n          .then(data => {\n            return data\n          })\n          .catch(err => {\n            return { content: null, error: err.message.includes('Timeout') ? 'Timeout API BOQ' : 'Errore API BOQ' }\n          })\n\n        const utilizzoPromise = fetchWithTimeout(`http://localhost:8001/api/reports/${currentCantiereId}/storico-bobine?formato=video`, {\n          headers: {\n            'Authorization': `Bearer ${localStorage.getItem('token')}`,\n            'Content-Type': 'application/json'\n          }\n        })\n          .then(res => {\n            return res.json()\n          })\n          .then(data => {\n            return data\n          })\n          .catch(err => {\n            return { content: null, error: err.message.includes('Timeout') ? 'Timeout API Utilizzo Bobine' : 'Errore API Utilizzo Bobine' }\n          })\n\n        // Wait for all promises to resolve (they won't reject due to the catch handlers)\n        const [progressData, boqData, utilizzoData] = await Promise.all([\n          progressPromise,\n          boqPromise,\n          utilizzoPromise\n        ])\n\n        // Debug: vediamo la struttura dei dati BOQ\n\n        // Set the data for each report - manteniamo la struttura completa con .content\n        setReportProgress(progressData)\n        setReportBOQ(boqData)\n        setReportUtilizzoBobine(utilizzoData)\n\n        // Check for timeout errors specifically\n        const hasTimeoutErrors = [progressData, boqData, utilizzoData].some(data =>\n          data?.error?.includes('Timeout')\n        )\n\n        // Only set error to null if we successfully loaded at least one report\n        if (progressData?.content || boqData?.content || utilizzoData?.content ||\n            progressData || boqData || utilizzoData) {\n          if (hasTimeoutErrors) {\n            setError('Alcuni report hanno riscontrato timeout. I dati disponibili sono mostrati sotto.')\n          } else {\n            setError('')\n          }\n        } else {\n          setError('Errore nel caricamento dei report. Riprova più tardi.')\n        }\n\n        // Always stop loading after processing the data\n        setIsLoading(false)\n      } catch (err) {\n        // This catch block should rarely be hit due to the individual error handling above\n        setError('Errore nel caricamento dei report. Riprova più tardi.')\n      } finally {\n        setIsLoading(false)\n      }\n    }\n\n    // Aspetta che l'autenticazione e il cantiere siano caricati\n    if (authLoading || cantiereLoading) {\n      console.log('🏗️ ReportsPage: Caricamento in corso, attendo...', { authLoading, cantiereLoading })\n      return\n    }\n\n    // Carica i report se c'è un cantiere valido\n    if (isValidCantiere && cantiereId && cantiereId > 0) {\n      console.log('🏗️ ReportsPage: Cantiere valido trovato, carico report')\n      loadAllReports()\n    } else {\n      console.warn('🏗️ ReportsPage: Nessun cantiere valido')\n      setIsLoading(false)\n      setError(cantiereError || 'Nessun cantiere selezionato. Seleziona un cantiere da \"Gestisci Cantieri\".')\n    }\n  }, [cantiereId, isValidCantiere, authLoading, cantiereLoading, cantiereError])\n\n  const handleRefresh = () => {\n    // Ricarica i report per il cantiere corrente\n    if (isValidCantiere && cantiereId) {\n      console.log('🏗️ ReportsPage: Refresh report per cantiere:', cantiereId)\n      setIsLoading(true)\n      setError('')\n      setReportProgress(null)\n      setReportBOQ(null)\n      setReportUtilizzoBobine(null)\n      setReportAvanzamento(null)\n      // Ricarica direttamente senza reload della pagina\n      loadAllReports()\n    } else {\n      console.warn('🏗️ ReportsPage: Impossibile fare refresh, nessun cantiere valido')\n    }\n  }\n\n  const handleExportReport = async (reportType: string, format: string = 'pdf') => {\n    try {\n      const currentCantiereId = cantiere?.id_cantiere\n      if (!currentCantiereId) {\n        return\n      }\n\n      let response\n      switch (reportType) {\n        case 'progress':\n          response = await reportsApi.getReportProgress(currentCantiereId)\n          break\n        case 'boq':\n          response = await reportsApi.getReportBOQ(currentCantiereId)\n          break\n        case 'utilizzo-bobine':\n          response = await reportsApi.getReportUtilizzoBobine(currentCantiereId)\n          break\n        default:\n          return\n      }\n\n      if (response.data.file_url) {\n        // Apri il file in una nuova finestra\n        window.open(response.data.file_url, '_blank')\n      }\n    } catch (error) {\n    }\n  }\n\n  // Calcolo IAP (Indice di Avanzamento Ponderato)\n  const calculateIAP = (nTot: number, nInst: number, nColl: number, nCert: number): number => {\n    const Wp = 2.0  // Peso fase Posa\n    const Wc = 1.5  // Peso fase Collegamento\n    const Wz = 0.5  // Peso fase Certificazione\n\n    if (nTot === 0) return 0\n\n    const sforzoSoloInstallati = (nInst - nColl) * Wp\n    const sforzoSoloCollegati = (nColl - nCert) * (Wp + Wc)\n    const sforzoCertificati = nCert * (Wp + Wc + Wz)\n    const numeratore = sforzoSoloInstallati + sforzoSoloCollegati + sforzoCertificati\n\n    const denominatore = nTot * (Wp + Wc + Wz)\n    return Math.round((numeratore / denominatore) * 10000) / 100\n  }\n\n  return (\n    <CantiereErrorBoundary>\n      <div className=\"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100\">\n        <div className=\"max-w-[90%] mx-auto py-6 space-y-6\">\n\n\n\n        {/* Loading State */}\n        {(isLoading || authLoading) ? (\n          <div className=\"flex items-center justify-center py-12\">\n            <div className=\"flex items-center gap-2\">\n              <Loader2 className=\"h-6 w-6 animate-spin\" />\n              <span>Caricamento report...</span>\n            </div>\n          </div>\n        ) : error ? (\n          <div className=\"p-6 border border-amber-200 rounded-lg bg-amber-50\">\n            <div className=\"flex items-center mb-4\">\n              <AlertCircle className=\"h-5 w-5 text-amber-600 mr-2\" />\n              <span className=\"text-amber-800 font-medium\">\n                {error.includes('Nessun cantiere selezionato') || error.includes('Cantiere non selezionato') ? 'Cantiere non selezionato' :\n                 error.includes('timeout') || error.includes('Timeout') ? 'Timeout API' : 'Errore caricamento report'}\n              </span>\n            </div>\n            <p className=\"text-amber-700 mb-4\">{error}</p>\n            {error.includes('timeout') || error.includes('Timeout') ? (\n              <div className=\"mb-4 p-3 bg-blue-50 border border-blue-200 rounded\">\n                <p className=\"text-blue-800 text-sm\">\n                  💡 <strong>Suggerimento:</strong> Le API stanno impiegando più tempo del previsto.\n                  Prova ad aggiornare la pagina o riprova tra qualche minuto.\n                </p>\n              </div>\n            ) : null}\n            <div className=\"flex gap-2\">\n              <Button\n                onClick={() => window.location.href = '/cantieri'}\n                className=\"bg-amber-600 hover:bg-amber-700 text-white\"\n              >\n                Gestisci Cantieri\n              </Button>\n              <Button\n                variant=\"outline\"\n                onClick={handleRefresh}\n                className=\"border-amber-600 text-amber-700 hover:bg-amber-100\"\n              >\n                <RefreshCw className=\"h-4 w-4 mr-2\" />\n                Riprova\n              </Button>\n            </div>\n          </div>\n        ) : (\n          <Tabs value={activeTab} onValueChange={setActiveTab} className=\"w-full\">\n            {/* Navigazione Tab Stile Admin */}\n            <div className=\"bg-white rounded-lg border border-slate-200 shadow-sm p-1 mb-6 mt-6\">\n              <div className=\"grid grid-cols-4 gap-1 h-auto bg-transparent p-0\">\n                <button\n                  onClick={() => setActiveTab('avanzamento')}\n                  className={`admin-tab-trigger flex items-center gap-2 px-4 py-3 rounded-md transition-all duration-200 border border-transparent ${\n                    activeTab === 'avanzamento'\n                      ? 'bg-blue-50 text-blue-700 border-blue-200 shadow-sm'\n                      : 'hover:bg-slate-50'\n                  }`}\n                  data-state={activeTab === 'avanzamento' ? 'active' : 'inactive'}\n                >\n                  <Target className=\"h-4 w-4\" />\n                  <span className=\"font-medium\">Avanzamento</span>\n                </button>\n                <button\n                  onClick={() => setActiveTab('boq')}\n                  className={`admin-tab-trigger flex items-center gap-2 px-4 py-3 rounded-md transition-all duration-200 border border-transparent ${\n                    activeTab === 'boq'\n                      ? 'bg-blue-50 text-blue-700 border-blue-200 shadow-sm'\n                      : 'hover:bg-slate-50'\n                  }`}\n                  data-state={activeTab === 'boq' ? 'active' : 'inactive'}\n                >\n                  <FileText className=\"h-4 w-4\" />\n                  <span className=\"font-medium\">BOQ</span>\n                </button>\n                <button\n                  onClick={() => setActiveTab('bobine')}\n                  className={`admin-tab-trigger flex items-center gap-2 px-4 py-3 rounded-md transition-all duration-200 border border-transparent ${\n                    activeTab === 'bobine'\n                      ? 'bg-blue-50 text-blue-700 border-blue-200 shadow-sm'\n                      : 'hover:bg-slate-50'\n                  }`}\n                  data-state={activeTab === 'bobine' ? 'active' : 'inactive'}\n                >\n                  <Package className=\"h-4 w-4\" />\n                  <span className=\"font-medium\">Bobine</span>\n                </button>\n                <button\n                  onClick={() => setActiveTab('produttivita')}\n                  className={`admin-tab-trigger flex items-center gap-2 px-4 py-3 rounded-md transition-all duration-200 border border-transparent ${\n                    activeTab === 'produttivita'\n                      ? 'bg-blue-50 text-blue-700 border-blue-200 shadow-sm'\n                      : 'hover:bg-slate-50'\n                  }`}\n                  data-state={activeTab === 'produttivita' ? 'active' : 'inactive'}\n                >\n                  <Zap className=\"h-4 w-4\" />\n                  <span className=\"font-medium\">Produttività</span>\n                </button>\n              </div>\n            </div>\n\n            {/* Tab Content: Avanzamento */}\n            <TabsContent value=\"avanzamento\" className=\"space-y-6\">\n              {reportProgress?.content ? (\n                <>\n                  {/* KPI Cards Moderne */}\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n                    <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200\">\n                      <div className=\"flex items-center justify-between mb-4\">\n                        <div className=\"p-2 bg-blue-50 rounded-lg\">\n                          <Target className=\"h-5 w-5 text-blue-600\" />\n                        </div>\n                        <div className=\"text-right\">\n                          <div className=\"text-2xl font-bold text-gray-900\">\n                            {reportProgress.content.metri_totali?.toLocaleString() || 0}\n                          </div>\n                          <div className=\"text-xs text-blue-600 font-medium\">metri</div>\n                        </div>\n                      </div>\n                      <div>\n                        <h3 className=\"text-sm font-medium text-gray-600 mb-1\">Metri Totali</h3>\n                        <p className=\"text-xs text-gray-500\">\n                          {reportProgress.content.totale_cavi || 0} cavi totali\n                        </p>\n                      </div>\n                    </div>\n\n                    <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200\">\n                      <div className=\"flex items-center justify-between mb-4\">\n                        <div className=\"p-2 bg-green-50 rounded-lg\">\n                          <CheckCircle className=\"h-5 w-5 text-green-600\" />\n                        </div>\n                        <div className=\"text-right\">\n                          <div className=\"text-2xl font-bold text-gray-900\">\n                            {reportProgress.content.metri_posati?.toLocaleString() || 0}\n                          </div>\n                          <div className=\"text-xs text-green-600 font-medium\">metri</div>\n                        </div>\n                      </div>\n                      <div>\n                        <h3 className=\"text-sm font-medium text-gray-600 mb-2\">Metri Posati</h3>\n                        <div className=\"w-full bg-gray-200 rounded-full h-2 mb-2\">\n                          <div\n                            className=\"bg-green-500 h-2 rounded-full transition-all duration-300\"\n                            style={{ width: `${Math.min(reportProgress.content.percentuale_avanzamento || 0, 100)}%` }}\n                          ></div>\n                        </div>\n                        <p className=\"text-xs text-gray-500\">\n                          {reportProgress.content.percentuale_avanzamento?.toFixed(1) || 0}% completato\n                        </p>\n                      </div>\n                    </div>\n\n                    <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200\">\n                      <div className=\"flex items-center justify-between mb-4\">\n                        <div className=\"p-2 bg-purple-50 rounded-lg\">\n                          <TrendingUp className=\"h-5 w-5 text-purple-600\" />\n                        </div>\n                        <div className=\"text-right\">\n                          <div className=\"text-2xl font-bold text-gray-900\">\n                            {reportProgress.content.media_giornaliera?.toFixed(1) || 0}\n                          </div>\n                          <div className=\"text-xs text-purple-600 font-medium\">m/giorno</div>\n                        </div>\n                      </div>\n                      <div>\n                        <h3 className=\"text-sm font-medium text-gray-600 mb-1\">Media Giornaliera</h3>\n                        <div className=\"flex items-center\">\n                          <Activity className=\"h-3 w-3 text-purple-500 mr-1\" />\n                          <span className=\"text-xs text-gray-500\">\n                            {reportProgress.content.giorni_lavorativi_effettivi || 0} giorni attivi\n                          </span>\n                        </div>\n                      </div>\n                    </div>\n\n                    <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200\">\n                      <div className=\"flex items-center justify-between mb-4\">\n                        <div className=\"p-2 bg-orange-50 rounded-lg\">\n                          <Clock className=\"h-5 w-5 text-orange-600\" />\n                        </div>\n                        <div className=\"text-right\">\n                          <div className=\"text-lg font-bold text-gray-900\">\n                            {reportProgress.content.data_completamento ?\n                              new Date(reportProgress.content.data_completamento).toLocaleDateString('it-IT') :\n                              'Da calcolare'\n                            }\n                          </div>\n                          <div className=\"text-xs text-orange-600 font-medium\">stima</div>\n                        </div>\n                      </div>\n                      <div>\n                        <h3 className=\"text-sm font-medium text-gray-600 mb-1\">Completamento</h3>\n                        <div className=\"flex items-center\">\n                          <Calendar className=\"h-3 w-3 text-orange-500 mr-1\" />\n                          <span className=\"text-xs text-gray-500\">\n                            {reportProgress.content.giorni_stimati || 0} giorni rimanenti\n                          </span>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Charts Moderni */}\n                  <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n                    {/* Posa Recente - Chart Migliorato */}\n                    <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n                      <div className=\"mb-6\">\n                        <h3 className=\"text-lg font-semibold text-gray-900 mb-1\">Posa Recente</h3>\n                        <p className=\"text-sm text-gray-600\">Ultimi 10 giorni di attività</p>\n                      </div>\n                      <div className=\"h-80\">\n                        <ResponsiveContainer width=\"100%\" height=\"100%\">\n                          <LineChart data={[...(reportProgress.content.posa_recente || [])].reverse()}>\n                            <CartesianGrid strokeDasharray=\"3 3\" stroke=\"#f1f5f9\" />\n                            <XAxis\n                              dataKey=\"data\"\n                              tick={{ fontSize: 12, fill: '#64748b' }}\n                              axisLine={{ stroke: '#e2e8f0' }}\n                            />\n                            <YAxis\n                              tick={{ fontSize: 12, fill: '#64748b' }}\n                              axisLine={{ stroke: '#e2e8f0' }}\n                            />\n                            <Tooltip\n                              contentStyle={{\n                                backgroundColor: 'white',\n                                border: '1px solid #e2e8f0',\n                                borderRadius: '8px',\n                                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',\n                                fontSize: '14px'\n                              }}\n                              labelStyle={{ color: '#374151', fontWeight: '500' }}\n                            />\n                            <Line\n                              type=\"monotone\"\n                              dataKey=\"metri\"\n                              stroke=\"#3b82f6\"\n                              strokeWidth={3}\n                              dot={{ fill: '#3b82f6', strokeWidth: 2, r: 4 }}\n                              activeDot={{ r: 6, stroke: '#3b82f6', strokeWidth: 2, fill: 'white' }}\n                              name=\"Metri Posati\"\n                            />\n                          </LineChart>\n                        </ResponsiveContainer>\n                      </div>\n                    </div>\n\n                    {/* Statistiche Cavi - Widget Moderno */}\n                    <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n                      <div className=\"mb-6\">\n                        <h3 className=\"text-lg font-semibold text-gray-900 mb-1\">Stato Cavi</h3>\n                        <p className=\"text-sm text-gray-600\">Distribuzione per stato di avanzamento</p>\n                      </div>\n                      <div className=\"space-y-6\">\n                        <div className=\"flex items-center justify-between p-4 bg-green-50 rounded-lg border border-green-200\">\n                          <div className=\"flex items-center gap-3\">\n                            <div className=\"w-3 h-3 bg-green-500 rounded-full\"></div>\n                            <span className=\"text-sm font-medium text-gray-700\">Cavi Posati</span>\n                          </div>\n                          <span className=\"text-lg font-bold text-green-700\">\n                            {reportProgress.content.cavi_posati || 0}\n                          </span>\n                        </div>\n                        <div className=\"flex items-center justify-between p-4 bg-gray-50 rounded-lg border border-gray-200\">\n                          <div className=\"flex items-center gap-3\">\n                            <div className=\"w-3 h-3 bg-gray-400 rounded-full\"></div>\n                            <span className=\"text-sm font-medium text-gray-700\">Cavi Rimanenti</span>\n                          </div>\n                          <span className=\"text-lg font-bold text-gray-700\">\n                            {reportProgress.content.cavi_rimanenti || 0}\n                          </span>\n                        </div>\n                        <div className=\"flex items-center justify-between p-4 bg-blue-50 rounded-lg border border-blue-200\">\n                          <div className=\"flex items-center gap-3\">\n                            <div className=\"w-3 h-3 bg-blue-500 rounded-full\"></div>\n                            <span className=\"text-sm font-medium text-gray-700\">Percentuale Completamento</span>\n                          </div>\n                          <span className=\"text-lg font-bold text-blue-700\">\n                            {reportProgress.content.percentuale_cavi?.toFixed(1) || 0}%\n                          </span>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </>\n              ) : (\n                <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center\">\n                  <Target className=\"h-12 w-12 mx-auto mb-4 text-gray-300\" />\n                  <h3 className=\"text-lg font-semibold text-gray-700 mb-2\">Nessun Dato Disponibile</h3>\n                  <p className=\"text-gray-500\">Nessun dato di avanzamento disponibile per questo cantiere</p>\n                </div>\n              )}\n            </TabsContent>\n\n            {/* Tab Content: BOQ */}\n            <TabsContent value=\"boq\" className=\"space-y-6\">\n              {reportBOQ?.error ? (\n                <div className=\"bg-white rounded-lg shadow-sm border border-amber-200 p-12 text-center\">\n                  <AlertCircle className=\"h-12 w-12 mx-auto mb-4 text-amber-500\" />\n                  <h3 className=\"text-lg font-semibold text-gray-700 mb-2\">API BOQ Temporaneamente Non Disponibile</h3>\n                  <p className=\"text-gray-500 mb-4\">Il servizio BOQ sta riscontrando problemi di performance.</p>\n                  <p className=\"text-sm text-gray-400\">Stiamo lavorando per risolvere il problema.</p>\n                </div>\n              ) : reportBOQ?.content ? (\n                <>\n\n\n                  {/* Alert Metri Orfani */}\n                  {reportBOQ.content.metri_orfani && reportBOQ.content.metri_orfani.metri_orfani_totali > 0 && (\n                    <Card className=\"border-red-200 bg-red-50\">\n                      <CardHeader className=\"pb-3\">\n                        <CardTitle className=\"text-red-700 flex items-center\">\n                          <AlertTriangle className=\"h-5 w-5 mr-2\" />\n                          🚨 METRI POSATI SENZA TRACCIABILITÀ BOBINA\n                        </CardTitle>\n                      </CardHeader>\n                      <CardContent>\n                        <p className=\"text-red-800 font-medium mb-2\">\n                          <strong>{reportBOQ.content.metri_orfani.metri_orfani_totali}m</strong> installati con BOBINA_VUOTA\n                          ({reportBOQ.content.metri_orfani.num_cavi_orfani} cavi)\n                        </p>\n                        <div className=\"text-sm text-red-700 space-y-1\">\n                          {Array.isArray(reportBOQ.content.metri_orfani.dettaglio_per_categoria) ?\n                            reportBOQ.content.metri_orfani.dettaglio_per_categoria.map((categoria: any, index: number) => (\n                              <div key={index}>\n                                • <strong>{categoria.tipologia} {categoria.formazione}</strong>: {categoria.metri_orfani}m ({categoria.num_cavi} cavi)\n                              </div>\n                            )) : (\n                              <div>Dettaglio metri orfani non disponibile</div>\n                            )\n                          }\n                        </div>\n                        <div className=\"mt-3 p-3 bg-amber-50 border border-amber-200 rounded\">\n                          <p className=\"text-amber-800 text-sm\">\n                            ⚠️ <strong>NOTA:</strong> I metri orfani NON sono inclusi nel calcolo acquisti.\n                            Prima di acquistare, verificare se questi metri possono essere associati a bobine esistenti.\n                          </p>\n                        </div>\n                      </CardContent>\n                    </Card>\n                  )}\n\n                  {/* Riepilogo Generale - KPI Cards Moderne */}\n                  {reportBOQ.content.riepilogo && (\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n                      <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200\">\n                        <div className=\"flex items-center justify-between mb-4\">\n                          <div className=\"p-2 bg-red-50 rounded-lg\">\n                            <AlertTriangle className=\"h-5 w-5 text-red-600\" />\n                          </div>\n                          <div className=\"text-right\">\n                            <div className=\"text-2xl font-bold text-gray-900\">\n                              {reportBOQ.content.riepilogo.totale_metri_mancanti?.toLocaleString() || 0}\n                            </div>\n                            <div className=\"text-xs text-red-600 font-medium\">metri</div>\n                          </div>\n                        </div>\n                        <div>\n                          <h3 className=\"text-sm font-medium text-gray-600 mb-1\">Metri da Acquistare</h3>\n                          <p className=\"text-xs text-gray-500\">per completamento progetto</p>\n                        </div>\n                      </div>\n\n                      <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200\">\n                        <div className=\"flex items-center justify-between mb-4\">\n                          <div className=\"p-2 bg-green-50 rounded-lg\">\n                            <Package className=\"h-5 w-5 text-green-600\" />\n                          </div>\n                          <div className=\"text-right\">\n                            <div className=\"text-2xl font-bold text-gray-900\">\n                              {reportBOQ.content.riepilogo.totale_metri_residui?.toLocaleString() || 0}\n                            </div>\n                            <div className=\"text-xs text-green-600 font-medium\">metri</div>\n                          </div>\n                        </div>\n                        <div>\n                          <h3 className=\"text-sm font-medium text-gray-600 mb-1\">Metri Residui</h3>\n                          <p className=\"text-xs text-gray-500\">disponibili in magazzino</p>\n                        </div>\n                      </div>\n\n                      <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200\">\n                        <div className=\"flex items-center justify-between mb-4\">\n                          <div className=\"p-2 bg-purple-50 rounded-lg\">\n                            <CheckCircle className=\"h-5 w-5 text-purple-600\" />\n                          </div>\n                          <div className=\"text-right\">\n                            <div className=\"text-2xl font-bold text-gray-900\">\n                              {reportBOQ.content.riepilogo.percentuale_completamento?.toFixed(1) || 0}\n                            </div>\n                            <div className=\"text-xs text-purple-600 font-medium\">%</div>\n                          </div>\n                        </div>\n                        <div>\n                          <h3 className=\"text-sm font-medium text-gray-600 mb-1\">Completamento</h3>\n                          <p className=\"text-xs text-gray-500\">progetto completato</p>\n                        </div>\n                      </div>\n\n                      <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200\">\n                        <div className=\"flex items-center justify-between mb-4\">\n                          <div className=\"p-2 bg-orange-50 rounded-lg\">\n                            <FileText className=\"h-5 w-5 text-orange-600\" />\n                          </div>\n                          <div className=\"text-right\">\n                            <div className=\"text-2xl font-bold text-gray-900\">\n                              {reportBOQ.content.riepilogo.categorie_necessitano_acquisto || 0}\n                            </div>\n                            <div className=\"text-xs text-orange-600 font-medium\">categorie</div>\n                          </div>\n                        </div>\n                        <div>\n                          <h3 className=\"text-sm font-medium text-gray-600 mb-1\">Categorie</h3>\n                          <p className=\"text-xs text-gray-500\">necessitano acquisto</p>\n                        </div>\n                      </div>\n                    </div>\n                  )}\n\n                  {/* Distinta Materiali - Tabella Moderna */}\n                  <div className=\"bg-white rounded-lg shadow-sm border border-gray-200\">\n                    <div className=\"overflow-x-auto\">\n                      <table className=\"w-full\">\n                        <thead className=\"bg-gray-50\">\n                          <tr>\n                            <th className=\"text-left p-4 text-xs font-medium text-gray-500 uppercase tracking-wider\">Tipologia</th>\n                            <th className=\"text-left p-4 text-xs font-medium text-gray-500 uppercase tracking-wider\">Formazione</th>\n                            <th className=\"text-right p-4 text-xs font-medium text-gray-500 uppercase tracking-wider\">Cavi</th>\n                            <th className=\"text-right p-4 text-xs font-medium text-gray-500 uppercase tracking-wider\">Metri Teorici</th>\n                            <th className=\"text-right p-4 text-xs font-medium text-gray-500 uppercase tracking-wider\">Metri Posati</th>\n                            <th className=\"text-right p-4 text-xs font-medium text-gray-500 uppercase tracking-wider\">Metri da Posare</th>\n                            <th className=\"text-right p-4 text-xs font-medium text-gray-500 uppercase tracking-wider\">Bobine</th>\n                            <th className=\"text-right p-4 text-xs font-medium text-gray-500 uppercase tracking-wider\">Metri Residui</th>\n                            <th className=\"text-right p-4 text-xs font-medium text-gray-500 uppercase tracking-wider\">Metri Mancanti</th>\n                            <th className=\"text-center p-4 text-xs font-medium text-gray-500 uppercase tracking-wider\">Acquisto</th>\n                          </tr>\n                        </thead>\n                        <tbody className=\"bg-white divide-y divide-gray-200\">\n                          {reportBOQ.content.distinta_materiali?.map((item: any, index: number) => (\n                            <tr key={index} className={`hover:bg-gray-50 transition-colors duration-150 ${item.ha_bobina_vuota ? 'bg-red-50' : ''}`}>\n                              <td className=\"p-4 text-sm font-medium text-gray-900\">{item.tipologia}</td>\n                              <td className=\"p-4 text-sm text-gray-700\">{item.formazione}</td>\n                              <td className=\"p-4 text-sm text-gray-700 text-right\">{item.num_cavi}</td>\n                              <td className=\"p-4 text-sm text-gray-700 text-right\">{item.metri_teorici_totali?.toLocaleString()}</td>\n                              <td className=\"p-4 text-sm text-gray-700 text-right\">{item.metri_reali_posati?.toLocaleString()}</td>\n                              <td className=\"p-4 text-sm text-gray-700 text-right\">{item.metri_da_posare?.toLocaleString()}</td>\n                              <td className=\"p-4 text-sm text-gray-700 text-right\">{item.num_bobine}</td>\n                              <td className=\"p-4 text-sm text-gray-700 text-right\">{item.metri_residui?.toLocaleString()}</td>\n                              <td className=\"p-4 text-sm font-medium text-right\">\n                                {item.metri_mancanti > 0 ? (\n                                  <span className=\"text-red-600\">{item.metri_mancanti?.toLocaleString()}m</span>\n                                ) : (\n                                  <span className=\"text-green-600\">0m</span>\n                                )}\n                              </td>\n                              <td className=\"p-4 text-center\">\n                                {item.necessita_acquisto ? (\n                                  <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800\">\n                                    Sì\n                                  </span>\n                                ) : (\n                                  <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800\">\n                                    No\n                                  </span>\n                                )}\n                              </td>\n                            </tr>\n                          ))}\n                        </tbody>\n                      </table>\n                    </div>\n                  </div>\n\n                  {/* Bobine per Tipo - Tabella Moderna */}\n                  <div className=\"bg-white rounded-lg shadow-sm border border-gray-200\">\n                    <div className=\"overflow-x-auto\">\n                      <table className=\"w-full\">\n                        <thead className=\"bg-gray-50\">\n                          <tr>\n                            <th className=\"text-left p-4 text-xs font-medium text-gray-500 uppercase tracking-wider\">Tipologia</th>\n                            <th className=\"text-left p-4 text-xs font-medium text-gray-500 uppercase tracking-wider\">Formazione</th>\n                            <th className=\"text-right p-4 text-xs font-medium text-gray-500 uppercase tracking-wider\">Numero Bobine</th>\n                            <th className=\"text-right p-4 text-xs font-medium text-gray-500 uppercase tracking-wider\">Metri Disponibili</th>\n                          </tr>\n                        </thead>\n                        <tbody className=\"bg-white divide-y divide-gray-200\">\n                          {reportBOQ.content.bobine_per_tipo?.map((item: any, index: number) => (\n                            <tr key={index} className=\"hover:bg-gray-50 transition-colors duration-150\">\n                              <td className=\"p-4 text-sm font-medium text-gray-900\">{item.tipologia}</td>\n                              <td className=\"p-4 text-sm text-gray-700\">{item.formazione}</td>\n                              <td className=\"p-4 text-sm text-gray-700 text-right\">{item.num_bobine}</td>\n                              <td className=\"p-4 text-sm font-medium text-right\">\n                                <span className={item.metri_disponibili > 0 ? 'text-green-600' : 'text-red-600'}>\n                                  {item.metri_disponibili?.toLocaleString()}m\n                                </span>\n                              </td>\n                            </tr>\n                          ))}\n                        </tbody>\n                      </table>\n                    </div>\n                  </div>\n                </>\n              ) : (\n                <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center\">\n                  <FileText className=\"h-12 w-12 mx-auto mb-4 text-gray-300\" />\n                  <h3 className=\"text-lg font-semibold text-gray-700 mb-2\">Nessun Dato Disponibile</h3>\n                  <p className=\"text-gray-500\">Nessun dato BOQ disponibile per questo cantiere</p>\n                </div>\n              )}\n            </TabsContent>\n\n            {/* Tab Content: Bobine */}\n            <TabsContent value=\"bobine\" className=\"space-y-6\">\n              {reportUtilizzoBobine?.content ? (\n                <>\n\n\n                  {/* Statistiche Bobine - KPI Cards Moderne */}\n                  <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n                    <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200\">\n                      <div className=\"flex items-center justify-between mb-4\">\n                        <div className=\"p-2 bg-blue-50 rounded-lg\">\n                          <Package className=\"h-5 w-5 text-blue-600\" />\n                        </div>\n                        <div className=\"text-right\">\n                          <div className=\"text-2xl font-bold text-gray-900\">\n                            {reportUtilizzoBobine.content.totale_bobine || 0}\n                          </div>\n                          <div className=\"text-xs text-blue-600 font-medium\">bobine</div>\n                        </div>\n                      </div>\n                      <div>\n                        <h3 className=\"text-sm font-medium text-gray-600 mb-1\">Totale Bobine</h3>\n                        <p className=\"text-xs text-gray-500\">bobine nel cantiere</p>\n                      </div>\n                    </div>\n\n                    <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200\">\n                      <div className=\"flex items-center justify-between mb-4\">\n                        <div className=\"p-2 bg-green-50 rounded-lg\">\n                          <Activity className=\"h-5 w-5 text-green-600\" />\n                        </div>\n                        <div className=\"text-right\">\n                          <div className=\"text-2xl font-bold text-gray-900\">\n                            {reportUtilizzoBobine.content.bobine?.filter((b: any) =>\n                              b.stato === 'In uso' || b.stato === 'Disponibile'\n                            ).length || 0}\n                          </div>\n                          <div className=\"text-xs text-green-600 font-medium\">attive</div>\n                        </div>\n                      </div>\n                      <div>\n                        <h3 className=\"text-sm font-medium text-gray-600 mb-1\">Bobine Attive</h3>\n                        <p className=\"text-xs text-gray-500\">disponibili/in uso</p>\n                      </div>\n                    </div>\n\n                    <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200\">\n                      <div className=\"flex items-center justify-between mb-4\">\n                        <div className=\"p-2 bg-purple-50 rounded-lg\">\n                          <TrendingUp className=\"h-5 w-5 text-purple-600\" />\n                        </div>\n                        <div className=\"text-right\">\n                          <div className=\"text-2xl font-bold text-gray-900\">\n                            {reportUtilizzoBobine.content.bobine?.length > 0 ?\n                              (reportUtilizzoBobine.content.bobine.reduce((acc: number, b: any) =>\n                                acc + (b.percentuale_utilizzo || 0), 0) / reportUtilizzoBobine.content.bobine.length\n                              ).toFixed(1) : 0}\n                          </div>\n                          <div className=\"text-xs text-purple-600 font-medium\">%</div>\n                        </div>\n                      </div>\n                      <div>\n                        <h3 className=\"text-sm font-medium text-gray-600 mb-1\">Utilizzo Medio</h3>\n                        <p className=\"text-xs text-gray-500\">utilizzo medio</p>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Lista Bobine - Tabella Moderna */}\n                  <div className=\"bg-white rounded-lg shadow-sm border border-gray-200\">\n                    <div className=\"overflow-x-auto\">\n                      <table className=\"w-full\">\n                        <thead className=\"bg-gray-50\">\n                          <tr>\n                            <th className=\"text-left p-4 text-xs font-medium text-gray-500 uppercase tracking-wider\">Codice</th>\n                            <th className=\"text-left p-4 text-xs font-medium text-gray-500 uppercase tracking-wider\">Tipologia</th>\n                            <th className=\"text-right p-4 text-xs font-medium text-gray-500 uppercase tracking-wider\">Metri Totali</th>\n                            <th className=\"text-right p-4 text-xs font-medium text-gray-500 uppercase tracking-wider\">Metri Utilizzati</th>\n                            <th className=\"text-right p-4 text-xs font-medium text-gray-500 uppercase tracking-wider\">Metri Residui</th>\n                            <th className=\"text-right p-4 text-xs font-medium text-gray-500 uppercase tracking-wider\">Utilizzo %</th>\n                            <th className=\"text-left p-4 text-xs font-medium text-gray-500 uppercase tracking-wider\">Stato</th>\n                            <th className=\"text-right p-4 text-xs font-medium text-gray-500 uppercase tracking-wider\">Cavi</th>\n                          </tr>\n                        </thead>\n                        <tbody className=\"bg-white divide-y divide-gray-200\">\n                          {reportUtilizzoBobine.content.bobine?.map((bobina: any, index: number) => (\n                            <tr key={index} className=\"hover:bg-gray-50 transition-colors duration-150\">\n                              <td className=\"p-4 text-sm font-medium text-gray-900\">{bobina.codice}</td>\n                              <td className=\"p-4 text-sm text-gray-700\">{bobina.tipologia}</td>\n                              <td className=\"p-4 text-sm text-gray-700 text-right\">{bobina.metri_totali?.toLocaleString()}</td>\n                              <td className=\"p-4 text-sm text-gray-700 text-right\">{bobina.metri_utilizzati?.toLocaleString()}</td>\n                              <td className=\"p-4 text-sm text-gray-700 text-right\">{bobina.metri_residui?.toLocaleString()}</td>\n                              <td className=\"p-4 text-sm text-gray-700 text-right\">\n                                <div className=\"flex items-center justify-end gap-2\">\n                                  <span className=\"text-sm font-medium\">{bobina.percentuale_utilizzo?.toFixed(1)}%</span>\n                                  <div className=\"w-16 bg-gray-200 rounded-full h-2\">\n                                    <div\n                                      className=\"bg-blue-500 h-2 rounded-full transition-all duration-300\"\n                                      style={{ width: `${Math.min(bobina.percentuale_utilizzo || 0, 100)}%` }}\n                                    ></div>\n                                  </div>\n                                </div>\n                              </td>\n                              <td className=\"p-4\">\n                                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${\n                                  bobina.stato === 'Disponibile' ? 'bg-green-100 text-green-800' :\n                                  bobina.stato === 'In uso' ? 'bg-blue-100 text-blue-800' :\n                                  bobina.stato === 'Terminata' ? 'bg-gray-100 text-gray-800' :\n                                  bobina.stato === 'Over' ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-800'\n                                }`}>\n                                  {bobina.stato}\n                                </span>\n                              </td>\n                              <td className=\"p-4 text-sm text-gray-700 text-right\">{bobina.totale_cavi_associati || 0}</td>\n                            </tr>\n                          ))}\n                        </tbody>\n                      </table>\n                    </div>\n                  </div>\n                </>\n              ) : (\n                <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center\">\n                  <Package className=\"h-12 w-12 mx-auto mb-4 text-gray-300\" />\n                  <h3 className=\"text-lg font-semibold text-gray-700 mb-2\">Nessun Dato Disponibile</h3>\n                  <p className=\"text-gray-500\">Nessun dato bobine disponibile per questo cantiere</p>\n                </div>\n              )}\n            </TabsContent>\n\n            {/* Tab Content: Produttività */}\n            <TabsContent value=\"produttivita\" className=\"space-y-6\">\n              <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center\">\n                <div className=\"p-4 bg-blue-50 rounded-full w-20 h-20 mx-auto mb-6 flex items-center justify-center\">\n                  <Zap className=\"h-10 w-10 text-blue-600\" />\n                </div>\n                <h3 className=\"text-xl font-semibold text-gray-900 mb-3\">Produttività</h3>\n                <p className=\"text-gray-600 mb-2\">Funzionalità in fase di sviluppo</p>\n                <p className=\"text-sm text-gray-500\">\n                  Includerà calcoli IAP, statistiche team e analisi performance\n                </p>\n              </div>\n            </TabsContent>\n\n          </Tabs>\n        )}\n\n        </div>\n      </div>\n    </CantiereErrorBoundary>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AAGA;AAEA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAkBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAjCA;;;;;;;;;;;;;AAkDe,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IAChE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IAChD,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IACtE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IAC1D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,EAAE,IAAI,EAAE,WAAW,WAAW,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC/C,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,eAAe,EAAE,WAAW,eAAe,EAAE,OAAO,aAAa,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,cAAW,AAAD;IAE9G,qFAAqF;IACrF,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QAER,MAAM,kBAAiB;YACrB,aAAa;YACb,IAAI;gBACF,sDAAsD;gBACtD,MAAM,oBAAoB;gBAE1B,iBAAiB;gBACjB,MAAM,QAAQ,aAAa,OAAO,CAAC;gBACnC,QAAQ,GAAG,CAAC,qDAAqD;gBAEjE,IAAI,CAAC,qBAAqB,qBAAqB,GAAG;oBAChD,QAAQ,IAAI,CAAC;oBACb,SAAS,iBAAiB;oBAC1B,aAAa;oBACb;gBACF;gBAEA,wFAAwF;gBACxF,6EAA6E;gBAE7E,gEAAgE;gBAChE,MAAM,mBAAmB,CAAC,KAAa,SAAc,UAAU,KAAK;oBAClE,OAAO,QAAQ,IAAI,CAAC;wBAClB,MAAM,KAAK;wBACX,IAAI,QAAQ,CAAC,GAAG,SACd,WAAW,IAAM,OAAO,IAAI,MAAM,CAAC,aAAa,EAAE,UAAQ,KAAK,MAAM,EAAE,KAAK,IAAI;qBAEnF;gBACH;gBAEA,MAAM,kBAAkB,iBAAiB,CAAC,kCAAkC,EAAE,kBAAkB,uBAAuB,CAAC,EAAE;oBACxH,SAAS;wBACP,iBAAiB,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,UAAU;wBAC1D,gBAAgB;oBAClB;gBACF,GACG,IAAI,CAAC,CAAA;oBACJ,OAAO,IAAI,IAAI;gBACjB,GACC,IAAI,CAAC,CAAA;oBACJ,OAAO;gBACT,GACC,KAAK,CAAC,CAAA;oBACL,OAAO;wBAAE,SAAS;wBAAM,OAAO,IAAI,OAAO,CAAC,QAAQ,CAAC,aAAa,yBAAyB;oBAAsB;gBAClH;gBAEF,MAAM,aAAa,iBAAiB,CAAC,kCAAkC,EAAE,kBAAkB,kBAAkB,CAAC,EAAE;oBAC9G,SAAS;wBACP,iBAAiB,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,UAAU;wBAC1D,gBAAgB;oBAClB;gBACF,GACG,IAAI,CAAC,CAAA;oBACJ,OAAO,IAAI,IAAI;gBACjB,GACC,IAAI,CAAC,CAAA;oBACJ,OAAO;gBACT,GACC,KAAK,CAAC,CAAA;oBACL,OAAO;wBAAE,SAAS;wBAAM,OAAO,IAAI,OAAO,CAAC,QAAQ,CAAC,aAAa,oBAAoB;oBAAiB;gBACxG;gBAEF,MAAM,kBAAkB,iBAAiB,CAAC,kCAAkC,EAAE,kBAAkB,6BAA6B,CAAC,EAAE;oBAC9H,SAAS;wBACP,iBAAiB,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,UAAU;wBAC1D,gBAAgB;oBAClB;gBACF,GACG,IAAI,CAAC,CAAA;oBACJ,OAAO,IAAI,IAAI;gBACjB,GACC,IAAI,CAAC,CAAA;oBACJ,OAAO;gBACT,GACC,KAAK,CAAC,CAAA;oBACL,OAAO;wBAAE,SAAS;wBAAM,OAAO,IAAI,OAAO,CAAC,QAAQ,CAAC,aAAa,gCAAgC;oBAA6B;gBAChI;gBAEF,iFAAiF;gBACjF,MAAM,CAAC,cAAc,SAAS,aAAa,GAAG,MAAM,QAAQ,GAAG,CAAC;oBAC9D;oBACA;oBACA;iBACD;gBAED,2CAA2C;gBAE3C,+EAA+E;gBAC/E,kBAAkB;gBAClB,aAAa;gBACb,wBAAwB;gBAExB,wCAAwC;gBACxC,MAAM,mBAAmB;oBAAC;oBAAc;oBAAS;iBAAa,CAAC,IAAI,CAAC,CAAA,OAClE,MAAM,OAAO,SAAS;gBAGxB,uEAAuE;gBACvE,IAAI,cAAc,WAAW,SAAS,WAAW,cAAc,WAC3D,gBAAgB,WAAW,cAAc;oBAC3C,IAAI,kBAAkB;wBACpB,SAAS;oBACX,OAAO;wBACL,SAAS;oBACX;gBACF,OAAO;oBACL,SAAS;gBACX;gBAEA,gDAAgD;gBAChD,aAAa;YACf,EAAE,OAAO,KAAK;gBACZ,mFAAmF;gBACnF,SAAS;YACX,SAAU;gBACR,aAAa;YACf;QACF;QAEA,4DAA4D;QAC5D,IAAI,eAAe,iBAAiB;YAClC,QAAQ,GAAG,CAAC,qDAAqD;gBAAE;gBAAa;YAAgB;YAChG;QACF;QAEA,4CAA4C;QAC5C,IAAI,mBAAmB,cAAc,aAAa,GAAG;YACnD,QAAQ,GAAG,CAAC;YACZ;QACF,OAAO;YACL,QAAQ,IAAI,CAAC;YACb,aAAa;YACb,SAAS,iBAAiB;QAC5B;IACF,GAAG;QAAC;QAAY;QAAiB;QAAa;QAAiB;KAAc;IAE7E,MAAM,gBAAgB;QACpB,6CAA6C;QAC7C,IAAI,mBAAmB,YAAY;YACjC,QAAQ,GAAG,CAAC,iDAAiD;YAC7D,aAAa;YACb,SAAS;YACT,kBAAkB;YAClB,aAAa;YACb,wBAAwB;YACxB,qBAAqB;YACrB,kDAAkD;YAClD;QACF,OAAO;YACL,QAAQ,IAAI,CAAC;QACf;IACF;IAEA,MAAM,qBAAqB,OAAO,YAAoB,SAAiB,KAAK;QAC1E,IAAI;YACF,MAAM,oBAAoB,UAAU;YACpC,IAAI,CAAC,mBAAmB;gBACtB;YACF;YAEA,IAAI;YACJ,OAAQ;gBACN,KAAK;oBACH,WAAW,MAAM,iHAAA,CAAA,aAAU,CAAC,iBAAiB,CAAC;oBAC9C;gBACF,KAAK;oBACH,WAAW,MAAM,iHAAA,CAAA,aAAU,CAAC,YAAY,CAAC;oBACzC;gBACF,KAAK;oBACH,WAAW,MAAM,iHAAA,CAAA,aAAU,CAAC,uBAAuB,CAAC;oBACpD;gBACF;oBACE;YACJ;YAEA,IAAI,SAAS,IAAI,CAAC,QAAQ,EAAE;gBAC1B,qCAAqC;gBACrC,OAAO,IAAI,CAAC,SAAS,IAAI,CAAC,QAAQ,EAAE;YACtC;QACF,EAAE,OAAO,OAAO,CAChB;IACF;IAEA,gDAAgD;IAChD,MAAM,eAAe,CAAC,MAAc,OAAe,OAAe;QAChE,MAAM,KAAK,IAAK,iBAAiB;;QACjC,MAAM,KAAK,IAAK,yBAAyB;;QACzC,MAAM,KAAK,IAAK,2BAA2B;;QAE3C,IAAI,SAAS,GAAG,OAAO;QAEvB,MAAM,uBAAuB,CAAC,QAAQ,KAAK,IAAI;QAC/C,MAAM,sBAAsB,CAAC,QAAQ,KAAK,IAAI,CAAC,KAAK,EAAE;QACtD,MAAM,oBAAoB,QAAQ,CAAC,KAAK,KAAK,EAAE;QAC/C,MAAM,aAAa,uBAAuB,sBAAsB;QAEhE,MAAM,eAAe,OAAO,CAAC,KAAK,KAAK,EAAE;QACzC,OAAO,KAAK,KAAK,CAAC,AAAC,aAAa,eAAgB,SAAS;IAC3D;IAEA,qBACE,8OAAC,uJAAA,CAAA,wBAAqB;kBACpB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BAKd,AAAC,aAAa,4BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iNAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;0CACnB,8OAAC;0CAAK;;;;;;;;;;;;;;;;2BAGR,sBACF,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,8OAAC;oCAAK,WAAU;8CACb,MAAM,QAAQ,CAAC,kCAAkC,MAAM,QAAQ,CAAC,8BAA8B,6BAC9F,MAAM,QAAQ,CAAC,cAAc,MAAM,QAAQ,CAAC,aAAa,gBAAgB;;;;;;;;;;;;sCAG9E,8OAAC;4BAAE,WAAU;sCAAuB;;;;;;wBACnC,MAAM,QAAQ,CAAC,cAAc,MAAM,QAAQ,CAAC,2BAC3C,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;;oCAAwB;kDAChC,8OAAC;kDAAO;;;;;;oCAAsB;;;;;;;;;;;mCAInC;sCACJ,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;oCACtC,WAAU;8CACX;;;;;;8CAGD,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS;oCACT,WAAU;;sDAEV,8OAAC,gNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;yCAM5C,8OAAC,gIAAA,CAAA,OAAI;oBAAC,OAAO;oBAAW,eAAe;oBAAc,WAAU;;sCAE7D,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,CAAC,qHAAqH,EAC/H,cAAc,gBACV,uDACA,qBACJ;wCACF,cAAY,cAAc,gBAAgB,WAAW;;0DAErD,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC;gDAAK,WAAU;0DAAc;;;;;;;;;;;;kDAEhC,8OAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,CAAC,qHAAqH,EAC/H,cAAc,QACV,uDACA,qBACJ;wCACF,cAAY,cAAc,QAAQ,WAAW;;0DAE7C,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC;gDAAK,WAAU;0DAAc;;;;;;;;;;;;kDAEhC,8OAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,CAAC,qHAAqH,EAC/H,cAAc,WACV,uDACA,qBACJ;wCACF,cAAY,cAAc,WAAW,WAAW;;0DAEhD,8OAAC,wMAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;0DACnB,8OAAC;gDAAK,WAAU;0DAAc;;;;;;;;;;;;kDAEhC,8OAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,CAAC,qHAAqH,EAC/H,cAAc,iBACV,uDACA,qBACJ;wCACF,cAAY,cAAc,iBAAiB,WAAW;;0DAEtD,8OAAC,gMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;0DACf,8OAAC;gDAAK,WAAU;0DAAc;;;;;;;;;;;;;;;;;;;;;;;sCAMpC,8OAAC,gIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAc,WAAU;sCACxC,gBAAgB,wBACf;;kDAEE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;;;;;;0EAEpB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACZ,eAAe,OAAO,CAAC,YAAY,EAAE,oBAAoB;;;;;;kFAE5D,8OAAC;wEAAI,WAAU;kFAAoC;;;;;;;;;;;;;;;;;;kEAGvD,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAAyC;;;;;;0EACvD,8OAAC;gEAAE,WAAU;;oEACV,eAAe,OAAO,CAAC,WAAW,IAAI;oEAAE;;;;;;;;;;;;;;;;;;;0DAK/C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC,2NAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;;;;;;0EAEzB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACZ,eAAe,OAAO,CAAC,YAAY,EAAE,oBAAoB;;;;;;kFAE5D,8OAAC;wEAAI,WAAU;kFAAqC;;;;;;;;;;;;;;;;;;kEAGxD,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAAyC;;;;;;0EACvD,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEACC,WAAU;oEACV,OAAO;wEAAE,OAAO,GAAG,KAAK,GAAG,CAAC,eAAe,OAAO,CAAC,uBAAuB,IAAI,GAAG,KAAK,CAAC,CAAC;oEAAC;;;;;;;;;;;0EAG7F,8OAAC;gEAAE,WAAU;;oEACV,eAAe,OAAO,CAAC,uBAAuB,EAAE,QAAQ,MAAM;oEAAE;;;;;;;;;;;;;;;;;;;0DAKvE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;oEAAC,WAAU;;;;;;;;;;;0EAExB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACZ,eAAe,OAAO,CAAC,iBAAiB,EAAE,QAAQ,MAAM;;;;;;kFAE3D,8OAAC;wEAAI,WAAU;kFAAsC;;;;;;;;;;;;;;;;;;kEAGzD,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAAyC;;;;;;0EACvD,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,0MAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;kFACpB,8OAAC;wEAAK,WAAU;;4EACb,eAAe,OAAO,CAAC,2BAA2B,IAAI;4EAAE;;;;;;;;;;;;;;;;;;;;;;;;;0DAMjE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;;;;;;0EAEnB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACZ,eAAe,OAAO,CAAC,kBAAkB,GACxC,IAAI,KAAK,eAAe,OAAO,CAAC,kBAAkB,EAAE,kBAAkB,CAAC,WACvE;;;;;;kFAGJ,8OAAC;wEAAI,WAAU;kFAAsC;;;;;;;;;;;;;;;;;;kEAGzD,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAAyC;;;;;;0EACvD,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,0MAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;kFACpB,8OAAC;wEAAK,WAAU;;4EACb,eAAe,OAAO,CAAC,cAAc,IAAI;4EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAQtD,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAA2C;;;;;;0EACzD,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;kEAEvC,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,mKAAA,CAAA,sBAAmB;4DAAC,OAAM;4DAAO,QAAO;sEACvC,cAAA,8OAAC,qJAAA,CAAA,YAAS;gEAAC,MAAM;uEAAK,eAAe,OAAO,CAAC,YAAY,IAAI,EAAE;iEAAE,CAAC,OAAO;;kFACvE,8OAAC,6JAAA,CAAA,gBAAa;wEAAC,iBAAgB;wEAAM,QAAO;;;;;;kFAC5C,8OAAC,qJAAA,CAAA,QAAK;wEACJ,SAAQ;wEACR,MAAM;4EAAE,UAAU;4EAAI,MAAM;wEAAU;wEACtC,UAAU;4EAAE,QAAQ;wEAAU;;;;;;kFAEhC,8OAAC,qJAAA,CAAA,QAAK;wEACJ,MAAM;4EAAE,UAAU;4EAAI,MAAM;wEAAU;wEACtC,UAAU;4EAAE,QAAQ;wEAAU;;;;;;kFAEhC,8OAAC,uJAAA,CAAA,UAAO;wEACN,cAAc;4EACZ,iBAAiB;4EACjB,QAAQ;4EACR,cAAc;4EACd,WAAW;4EACX,UAAU;wEACZ;wEACA,YAAY;4EAAE,OAAO;4EAAW,YAAY;wEAAM;;;;;;kFAEpD,8OAAC,oJAAA,CAAA,OAAI;wEACH,MAAK;wEACL,SAAQ;wEACR,QAAO;wEACP,aAAa;wEACb,KAAK;4EAAE,MAAM;4EAAW,aAAa;4EAAG,GAAG;wEAAE;wEAC7C,WAAW;4EAAE,GAAG;4EAAG,QAAQ;4EAAW,aAAa;4EAAG,MAAM;wEAAQ;wEACpE,MAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAQf,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAA2C;;;;;;0EACzD,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;kEAEvC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAI,WAAU;;;;;;0FACf,8OAAC;gFAAK,WAAU;0FAAoC;;;;;;;;;;;;kFAEtD,8OAAC;wEAAK,WAAU;kFACb,eAAe,OAAO,CAAC,WAAW,IAAI;;;;;;;;;;;;0EAG3C,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAI,WAAU;;;;;;0FACf,8OAAC;gFAAK,WAAU;0FAAoC;;;;;;;;;;;;kFAEtD,8OAAC;wEAAK,WAAU;kFACb,eAAe,OAAO,CAAC,cAAc,IAAI;;;;;;;;;;;;0EAG9C,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAI,WAAU;;;;;;0FACf,8OAAC;gFAAK,WAAU;0FAAoC;;;;;;;;;;;;kFAEtD,8OAAC;wEAAK,WAAU;;4EACb,eAAe,OAAO,CAAC,gBAAgB,EAAE,QAAQ,MAAM;4EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;6DAQtE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC;wCAAG,WAAU;kDAA2C;;;;;;kDACzD,8OAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;;;;;;sCAMnC,8OAAC,gIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAM,WAAU;sCAChC,WAAW,sBACV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;kDACvB,8OAAC;wCAAG,WAAU;kDAA2C;;;;;;kDACzD,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAClC,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;uCAErC,WAAW,wBACb;;oCAIG,UAAU,OAAO,CAAC,YAAY,IAAI,UAAU,OAAO,CAAC,YAAY,CAAC,mBAAmB,GAAG,mBACtF,8OAAC,gIAAA,CAAA,OAAI;wCAAC,WAAU;;0DACd,8OAAC,gIAAA,CAAA,aAAU;gDAAC,WAAU;0DACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;oDAAC,WAAU;;sEACnB,8OAAC,wNAAA,CAAA,gBAAa;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;0DAI9C,8OAAC,gIAAA,CAAA,cAAW;;kEACV,8OAAC;wDAAE,WAAU;;0EACX,8OAAC;;oEAAQ,UAAU,OAAO,CAAC,YAAY,CAAC,mBAAmB;oEAAC;;;;;;;4DAAU;4DACpE,UAAU,OAAO,CAAC,YAAY,CAAC,eAAe;4DAAC;;;;;;;kEAEnD,8OAAC;wDAAI,WAAU;kEACZ,MAAM,OAAO,CAAC,UAAU,OAAO,CAAC,YAAY,CAAC,uBAAuB,IACnE,UAAU,OAAO,CAAC,YAAY,CAAC,uBAAuB,CAAC,GAAG,CAAC,CAAC,WAAgB,sBAC1E,8OAAC;;oEAAgB;kFACb,8OAAC;;4EAAQ,UAAU,SAAS;4EAAC;4EAAE,UAAU,UAAU;;;;;;;oEAAU;oEAAG,UAAU,YAAY;oEAAC;oEAAI,UAAU,QAAQ;oEAAC;;+DADxG;;;;sFAIV,8OAAC;sEAAI;;;;;;;;;;;kEAIX,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAE,WAAU;;gEAAyB;8EACjC,8OAAC;8EAAO;;;;;;gEAAc;;;;;;;;;;;;;;;;;;;;;;;;oCASlC,UAAU,OAAO,CAAC,SAAS,kBAC1B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC,wNAAA,CAAA,gBAAa;oEAAC,WAAU;;;;;;;;;;;0EAE3B,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACZ,UAAU,OAAO,CAAC,SAAS,CAAC,qBAAqB,EAAE,oBAAoB;;;;;;kFAE1E,8OAAC;wEAAI,WAAU;kFAAmC;;;;;;;;;;;;;;;;;;kEAGtD,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAAyC;;;;;;0EACvD,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;0DAIzC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC,wMAAA,CAAA,UAAO;oEAAC,WAAU;;;;;;;;;;;0EAErB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACZ,UAAU,OAAO,CAAC,SAAS,CAAC,oBAAoB,EAAE,oBAAoB;;;;;;kFAEzE,8OAAC;wEAAI,WAAU;kFAAqC;;;;;;;;;;;;;;;;;;kEAGxD,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAAyC;;;;;;0EACvD,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;0DAIzC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC,2NAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;;;;;;0EAEzB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACZ,UAAU,OAAO,CAAC,SAAS,CAAC,yBAAyB,EAAE,QAAQ,MAAM;;;;;;kFAExE,8OAAC;wEAAI,WAAU;kFAAsC;;;;;;;;;;;;;;;;;;kEAGzD,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAAyC;;;;;;0EACvD,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;0DAIzC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;;;;;;0EAEtB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACZ,UAAU,OAAO,CAAC,SAAS,CAAC,8BAA8B,IAAI;;;;;;kFAEjE,8OAAC;wEAAI,WAAU;kFAAsC;;;;;;;;;;;;;;;;;;kEAGzD,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAAyC;;;;;;0EACvD,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;;;;;;;kDAO7C,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAM,WAAU;;kEACf,8OAAC;wDAAM,WAAU;kEACf,cAAA,8OAAC;;8EACC,8OAAC;oEAAG,WAAU;8EAA2E;;;;;;8EACzF,8OAAC;oEAAG,WAAU;8EAA2E;;;;;;8EACzF,8OAAC;oEAAG,WAAU;8EAA4E;;;;;;8EAC1F,8OAAC;oEAAG,WAAU;8EAA4E;;;;;;8EAC1F,8OAAC;oEAAG,WAAU;8EAA4E;;;;;;8EAC1F,8OAAC;oEAAG,WAAU;8EAA4E;;;;;;8EAC1F,8OAAC;oEAAG,WAAU;8EAA4E;;;;;;8EAC1F,8OAAC;oEAAG,WAAU;8EAA4E;;;;;;8EAC1F,8OAAC;oEAAG,WAAU;8EAA4E;;;;;;8EAC1F,8OAAC;oEAAG,WAAU;8EAA6E;;;;;;;;;;;;;;;;;kEAG/F,8OAAC;wDAAM,WAAU;kEACd,UAAU,OAAO,CAAC,kBAAkB,EAAE,IAAI,CAAC,MAAW,sBACrD,8OAAC;gEAAe,WAAW,CAAC,gDAAgD,EAAE,KAAK,eAAe,GAAG,cAAc,IAAI;;kFACrH,8OAAC;wEAAG,WAAU;kFAAyC,KAAK,SAAS;;;;;;kFACrE,8OAAC;wEAAG,WAAU;kFAA6B,KAAK,UAAU;;;;;;kFAC1D,8OAAC;wEAAG,WAAU;kFAAwC,KAAK,QAAQ;;;;;;kFACnE,8OAAC;wEAAG,WAAU;kFAAwC,KAAK,oBAAoB,EAAE;;;;;;kFACjF,8OAAC;wEAAG,WAAU;kFAAwC,KAAK,kBAAkB,EAAE;;;;;;kFAC/E,8OAAC;wEAAG,WAAU;kFAAwC,KAAK,eAAe,EAAE;;;;;;kFAC5E,8OAAC;wEAAG,WAAU;kFAAwC,KAAK,UAAU;;;;;;kFACrE,8OAAC;wEAAG,WAAU;kFAAwC,KAAK,aAAa,EAAE;;;;;;kFAC1E,8OAAC;wEAAG,WAAU;kFACX,KAAK,cAAc,GAAG,kBACrB,8OAAC;4EAAK,WAAU;;gFAAgB,KAAK,cAAc,EAAE;gFAAiB;;;;;;iGAEtE,8OAAC;4EAAK,WAAU;sFAAiB;;;;;;;;;;;kFAGrC,8OAAC;wEAAG,WAAU;kFACX,KAAK,kBAAkB,iBACtB,8OAAC;4EAAK,WAAU;sFAAkG;;;;;iGAIlH,8OAAC;4EAAK,WAAU;sFAAoG;;;;;;;;;;;;+DAtBjH;;;;;;;;;;;;;;;;;;;;;;;;;;kDAmCnB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAM,WAAU;;kEACf,8OAAC;wDAAM,WAAU;kEACf,cAAA,8OAAC;;8EACC,8OAAC;oEAAG,WAAU;8EAA2E;;;;;;8EACzF,8OAAC;oEAAG,WAAU;8EAA2E;;;;;;8EACzF,8OAAC;oEAAG,WAAU;8EAA4E;;;;;;8EAC1F,8OAAC;oEAAG,WAAU;8EAA4E;;;;;;;;;;;;;;;;;kEAG9F,8OAAC;wDAAM,WAAU;kEACd,UAAU,OAAO,CAAC,eAAe,EAAE,IAAI,CAAC,MAAW,sBAClD,8OAAC;gEAAe,WAAU;;kFACxB,8OAAC;wEAAG,WAAU;kFAAyC,KAAK,SAAS;;;;;;kFACrE,8OAAC;wEAAG,WAAU;kFAA6B,KAAK,UAAU;;;;;;kFAC1D,8OAAC;wEAAG,WAAU;kFAAwC,KAAK,UAAU;;;;;;kFACrE,8OAAC;wEAAG,WAAU;kFACZ,cAAA,8OAAC;4EAAK,WAAW,KAAK,iBAAiB,GAAG,IAAI,mBAAmB;;gFAC9D,KAAK,iBAAiB,EAAE;gFAAiB;;;;;;;;;;;;;+DANvC;;;;;;;;;;;;;;;;;;;;;;;;;;;6DAiBrB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;wCAAG,WAAU;kDAA2C;;;;;;kDACzD,8OAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;;;;;;sCAMnC,8OAAC,gIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAS,WAAU;sCACnC,sBAAsB,wBACrB;;kDAIE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC,wMAAA,CAAA,UAAO;oEAAC,WAAU;;;;;;;;;;;0EAErB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACZ,qBAAqB,OAAO,CAAC,aAAa,IAAI;;;;;;kFAEjD,8OAAC;wEAAI,WAAU;kFAAoC;;;;;;;;;;;;;;;;;;kEAGvD,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAAyC;;;;;;0EACvD,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;0DAIzC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;;;;;;0EAEtB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACZ,qBAAqB,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,IAC5C,EAAE,KAAK,KAAK,YAAY,EAAE,KAAK,KAAK,eACpC,UAAU;;;;;;kFAEd,8OAAC;wEAAI,WAAU;kFAAqC;;;;;;;;;;;;;;;;;;kEAGxD,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAAyC;;;;;;0EACvD,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;0DAIzC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;oEAAC,WAAU;;;;;;;;;;;0EAExB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACZ,qBAAqB,OAAO,CAAC,MAAM,EAAE,SAAS,IAC7C,CAAC,qBAAqB,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,KAAa,IACxD,MAAM,CAAC,EAAE,oBAAoB,IAAI,CAAC,GAAG,KAAK,qBAAqB,OAAO,CAAC,MAAM,CAAC,MAAM,AACtF,EAAE,OAAO,CAAC,KAAK;;;;;;kFAEnB,8OAAC;wEAAI,WAAU;kFAAsC;;;;;;;;;;;;;;;;;;kEAGzD,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAAyC;;;;;;0EACvD,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;;;;;;;kDAM3C,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAM,WAAU;;kEACf,8OAAC;wDAAM,WAAU;kEACf,cAAA,8OAAC;;8EACC,8OAAC;oEAAG,WAAU;8EAA2E;;;;;;8EACzF,8OAAC;oEAAG,WAAU;8EAA2E;;;;;;8EACzF,8OAAC;oEAAG,WAAU;8EAA4E;;;;;;8EAC1F,8OAAC;oEAAG,WAAU;8EAA4E;;;;;;8EAC1F,8OAAC;oEAAG,WAAU;8EAA4E;;;;;;8EAC1F,8OAAC;oEAAG,WAAU;8EAA4E;;;;;;8EAC1F,8OAAC;oEAAG,WAAU;8EAA2E;;;;;;8EACzF,8OAAC;oEAAG,WAAU;8EAA4E;;;;;;;;;;;;;;;;;kEAG9F,8OAAC;wDAAM,WAAU;kEACd,qBAAqB,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,QAAa,sBACtD,8OAAC;gEAAe,WAAU;;kFACxB,8OAAC;wEAAG,WAAU;kFAAyC,OAAO,MAAM;;;;;;kFACpE,8OAAC;wEAAG,WAAU;kFAA6B,OAAO,SAAS;;;;;;kFAC3D,8OAAC;wEAAG,WAAU;kFAAwC,OAAO,YAAY,EAAE;;;;;;kFAC3E,8OAAC;wEAAG,WAAU;kFAAwC,OAAO,gBAAgB,EAAE;;;;;;kFAC/E,8OAAC;wEAAG,WAAU;kFAAwC,OAAO,aAAa,EAAE;;;;;;kFAC5E,8OAAC;wEAAG,WAAU;kFACZ,cAAA,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAK,WAAU;;wFAAuB,OAAO,oBAAoB,EAAE,QAAQ;wFAAG;;;;;;;8FAC/E,8OAAC;oFAAI,WAAU;8FACb,cAAA,8OAAC;wFACC,WAAU;wFACV,OAAO;4FAAE,OAAO,GAAG,KAAK,GAAG,CAAC,OAAO,oBAAoB,IAAI,GAAG,KAAK,CAAC,CAAC;wFAAC;;;;;;;;;;;;;;;;;;;;;;kFAK9E,8OAAC;wEAAG,WAAU;kFACZ,cAAA,8OAAC;4EAAK,WAAW,CAAC,wEAAwE,EACxF,OAAO,KAAK,KAAK,gBAAgB,gCACjC,OAAO,KAAK,KAAK,WAAW,8BAC5B,OAAO,KAAK,KAAK,cAAc,8BAC/B,OAAO,KAAK,KAAK,SAAS,4BAA4B,6BACtD;sFACC,OAAO,KAAK;;;;;;;;;;;kFAGjB,8OAAC;wEAAG,WAAU;kFAAwC,OAAO,qBAAqB,IAAI;;;;;;;+DA3B/E;;;;;;;;;;;;;;;;;;;;;;;;;;;6DAoCrB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,wMAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;kDACnB,8OAAC;wCAAG,WAAU;kDAA2C;;;;;;kDACzD,8OAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;;;;;;sCAMnC,8OAAC,gIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAe,WAAU;sCAC1C,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;;;;;;kDAEjB,8OAAC;wCAAG,WAAU;kDAA2C;;;;;;kDACzD,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAClC,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAarD", "debugId": null}}]}