{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAuth } from '@/contexts/AuthContext'\n\nexport default function Home() {\n  const { user, cantiere, isAuthenticated, isLoading } = useAuth()\n  const router = useRouter()\n\n  useEffect(() => {\n    console.log('🏠 HomePage: Stato corrente:', {\n      isLoading,\n      isAuthenticated,\n      user: user ? { id: user.id_utente, ruolo: user.ruolo } : null,\n      cantiere: cantiere ? { id: cantiere.id_cantiere } : null\n    })\n\n    // ASPETTA che il caricamento sia completato\n    if (isLoading) {\n      console.log('🏠 HomePage: Ancora in caricamento, attendo...')\n      return\n    }\n\n    // Se NON autenticato, reindirizza al login\n    if (!isAuthenticated) {\n      console.log('🏠 HomePage: Utente NON autenticato, reindirizzamento a /login')\n      router.replace('/login')\n      return\n    }\n\n    // Se autenticato, reindirizza in base al ruolo SOLO se abbiamo dati validi\n    if (isAuthenticated && user) {\n      console.log('🏠 HomePage: Utente autenticato, reindirizzamento in base al ruolo:', user.ruolo)\n\n      if (user.ruolo === 'owner') {\n        console.log('🏠 HomePage: Reindirizzamento admin a /admin')\n        router.replace('/admin')\n      } else if (user.ruolo === 'user') {\n        console.log('🏠 HomePage: Reindirizzamento utente standard a /cantieri')\n        router.replace('/cantieri')\n      } else if (user.ruolo === 'cantieri_user') {\n        console.log('🏠 HomePage: Reindirizzamento utente cantiere a /cavi')\n        router.replace('/cavi')\n      } else {\n        console.log('🏠 HomePage: Ruolo sconosciuto, reindirizzamento a /login')\n        router.replace('/login')\n      }\n    } else if (isAuthenticated && cantiere && !user) {\n      // Login cantiere senza utente\n      console.log('🏠 HomePage: Login cantiere rilevato, reindirizzamento a /cavi')\n      router.replace('/cavi')\n    } else if (isAuthenticated && !user && !cantiere) {\n      // Stato inconsistente - autenticato ma senza dati\n      console.error('🏠 HomePage: Stato inconsistente - autenticato ma senza user/cantiere')\n      router.replace('/login')\n    }\n  }, [isAuthenticated, isLoading, user, cantiere, router])\n\n  // Mostra un indicatore di caricamento durante il reindirizzamento\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 flex items-center justify-center\">\n      <div className=\"text-center\">\n        <h1 className=\"text-3xl font-bold text-slate-900 mb-4\">\n          CABLYS\n        </h1>\n        <p className=\"text-slate-600 mb-6\">\n          {isLoading ? 'Verifica autenticazione...' : 'Reindirizzamento in corso...'}\n        </p>\n        <div className=\"w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto\"></div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,eAAe,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC7D,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ,GAAG,CAAC,gCAAgC;YAC1C;YACA;YACA,MAAM,OAAO;gBAAE,IAAI,KAAK,SAAS;gBAAE,OAAO,KAAK,KAAK;YAAC,IAAI;YACzD,UAAU,WAAW;gBAAE,IAAI,SAAS,WAAW;YAAC,IAAI;QACtD;QAEA,4CAA4C;QAC5C,IAAI,WAAW;YACb,QAAQ,GAAG,CAAC;YACZ;QACF;QAEA,2CAA2C;QAC3C,IAAI,CAAC,iBAAiB;YACpB,QAAQ,GAAG,CAAC;YACZ,OAAO,OAAO,CAAC;YACf;QACF;QAEA,2EAA2E;QAC3E,IAAI,mBAAmB,MAAM;YAC3B,QAAQ,GAAG,CAAC,uEAAuE,KAAK,KAAK;YAE7F,IAAI,KAAK,KAAK,KAAK,SAAS;gBAC1B,QAAQ,GAAG,CAAC;gBACZ,OAAO,OAAO,CAAC;YACjB,OAAO,IAAI,KAAK,KAAK,KAAK,QAAQ;gBAChC,QAAQ,GAAG,CAAC;gBACZ,OAAO,OAAO,CAAC;YACjB,OAAO,IAAI,KAAK,KAAK,KAAK,iBAAiB;gBACzC,QAAQ,GAAG,CAAC;gBACZ,OAAO,OAAO,CAAC;YACjB,OAAO;gBACL,QAAQ,GAAG,CAAC;gBACZ,OAAO,OAAO,CAAC;YACjB;QACF,OAAO,IAAI,mBAAmB,YAAY,CAAC,MAAM;YAC/C,8BAA8B;YAC9B,QAAQ,GAAG,CAAC;YACZ,OAAO,OAAO,CAAC;QACjB,OAAO,IAAI,mBAAmB,CAAC,QAAQ,CAAC,UAAU;YAChD,kDAAkD;YAClD,QAAQ,KAAK,CAAC;YACd,OAAO,OAAO,CAAC;QACjB;IACF,GAAG;QAAC;QAAiB;QAAW;QAAM;QAAU;KAAO;IAEvD,kEAAkE;IAClE,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAAyC;;;;;;8BAGvD,8OAAC;oBAAE,WAAU;8BACV,YAAY,+BAA+B;;;;;;8BAE9C,8OAAC;oBAAI,WAAU;;;;;;;;;;;;;;;;;AAIvB", "debugId": null}}]}