/**
 * @license lucide-react v0.516.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M12 3v18", key: "108xh3" }],
  ["path", { d: "m16 16 4-4-4-4", key: "1js579" }],
  ["path", { d: "m8 8-4 4 4 4", key: "1whems" }]
];
const SeparatorVertical = createLucideIcon("separator-vertical", __iconNode);

export { __iconNode, SeparatorVertical as default };
//# sourceMappingURL=separator-vertical.js.map
