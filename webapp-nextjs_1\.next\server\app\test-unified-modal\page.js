(()=>{var e={};e.id=804,e.ids=[804],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5529:(e,i,t)=>{"use strict";t.r(i),t.d(i,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var r=t(65239),s=t(48088),a=t(88170),n=t.n(a),l=t(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);t.d(i,o);let d={children:["",{children:["test-unified-modal",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,63548)),"C:\\CMS\\webapp-nextjs\\src\\app\\test-unified-modal\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\CMS\\webapp-nextjs\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\CMS\\webapp-nextjs\\src\\app\\test-unified-modal\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/test-unified-modal/page",pathname:"/test-unified-modal",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63548:(e,i,t)=>{"use strict";t.r(i),t.d(i,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\test-unified-modal\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\CMS\\webapp-nextjs\\src\\app\\test-unified-modal\\page.tsx","default")},70440:(e,i,t)=>{"use strict";t.r(i),t.d(i,{default:()=>s});var r=t(31658);let s=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},76628:(e,i,t)=>{"use strict";t.d(i,{jV:()=>a});var r=t(43210),s=t(63213);function a(){let{cantiere:e,isLoading:i}=(0,s.A)(),[t,a]=(0,r.useState)(null),[n,l]=(0,r.useState)(!0),[o,d]=(0,r.useState)(null);return{cantiereId:t,cantiere:e,isValidCantiere:null!==t&&t>0,isLoading:n,error:o,validateCantiere:e=>{if(null==e)return!1;let i="string"==typeof e?parseInt(e,10):e;return!isNaN(i)&&!(i<=0)||(console.warn("\uD83C\uDFD7️ useCantiere: ID cantiere non valido:",e),!1)},clearError:()=>d(null)}}},79551:e=>{"use strict";e.exports=require("url")},80013:(e,i,t)=>{"use strict";t.d(i,{J:()=>n});var r=t(60687);t(43210);var s=t(78148),a=t(4780);function n({className:e,...i}){return(0,r.jsx)(s.b,{"data-slot":"label",className:(0,a.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...i})}},81630:e=>{"use strict";e.exports=require("http")},81902:(e,i,t)=>{Promise.resolve().then(t.bind(t,63548))},83997:e=>{"use strict";e.exports=require("tty")},84027:(e,i,t)=>{"use strict";t.d(i,{A:()=>r});let r=(0,t(62688).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},89667:(e,i,t)=>{"use strict";t.d(i,{p:()=>n});var r=t(60687),s=t(43210),a=t(4780);let n=s.forwardRef(({className:e,type:i,...t},s)=>(0,r.jsx)("input",{type:i,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),ref:s,...t}));n.displayName="Input"},92406:(e,i,t)=>{"use strict";t.r(i),t.d(i,{default:()=>c});var r=t(60687),s=t(43210),a=t(29523),n=t(49039);let l={id_cavo:"C001",id_cantiere:1,revisione_ufficiale:"Rev 1.0",tipologia:"LIYCY",sezione:"3X2.5MM",formazione:"3X2.5MM",da:"Quadro A",a:"Quadro B",ubicazione_partenza:"Quadro A",ubicazione_arrivo:"Quadro B",metri_teorici:150,metri_posati:75,metratura_reale:75,stato_installazione:"installato",id_bobina:"BOB001"},o={id_cantiere:1,commessa:"TEST-001"},d=()=>{let[e,i]=(0,s.useState)(!1),[t,d]=(0,s.useState)(!1),c=async e=>{console.log("\uD83D\uDCBE Unified Modal Save:",e),await new Promise(e=>setTimeout(e,1e3)),alert(`Operazione completata con successo!
Modalit\xe0: ${e.mode}
Cavo: ${e.cableId}`)};return(0,r.jsxs)("div",{className:"p-6 space-y-4",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold mb-4",children:"Test Interfaccia Unificata"}),(0,r.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg",children:[(0,r.jsxs)("h3",{className:"font-semibold mb-2",children:["Cavo di Test: ",l.id_cavo]}),(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:[l.tipologia," ",l.sezione," - Da: ",l.da," A: ",l.a]}),(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:["Metri posati: ",l.metri_posati,"m / ",l.metri_teorici,"m"]})]}),(0,r.jsxs)("div",{className:"flex gap-4",children:[(0,r.jsx)(a.$,{onClick:()=>i(!0),className:"bg-green-600 hover:bg-green-700",children:"Test Modalit\xe0: Aggiungi Metri"}),(0,r.jsx)(a.$,{onClick:()=>d(!0),className:"bg-blue-600 hover:bg-blue-700",children:"Test Modalit\xe0: Modifica Bobina"})]}),(0,r.jsx)(n.B,{mode:"aggiungi_metri",open:e,onClose:()=>i(!1),cavo:l,cantiere:o,onSave:c}),(0,r.jsx)(n.B,{mode:"modifica_bobina",open:t,onClose:()=>d(!1),cavo:l,cantiere:o,onSave:c})]})};function c(){return(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 py-8",children:(0,r.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-center mb-8 text-gray-800",children:"Test Interfaccia Unificata Cable/Bobbin"}),(0,r.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6",children:[(0,r.jsx)("h2",{className:"font-semibold text-blue-800 mb-2",children:"Istruzioni per il Test"}),(0,r.jsxs)("ul",{className:"text-blue-700 text-sm space-y-1",children:[(0,r.jsx)("li",{children:"• Clicca sui pulsanti per aprire la modale unificata in diverse modalit\xe0"}),(0,r.jsx)("li",{children:"• Testa la validazione dei campi e la selezione delle bobine"}),(0,r.jsx)("li",{children:"• Verifica che le sezioni dinamiche si mostrino correttamente"}),(0,r.jsx)("li",{children:"• Controlla l'accessibilit\xe0 con Tab e Escape"}),(0,r.jsx)("li",{children:"• Verifica i messaggi di errore e successo"})]})]}),(0,r.jsx)(d,{}),(0,r.jsxs)("div",{className:"mt-8 bg-green-50 border border-green-200 rounded-lg p-4",children:[(0,r.jsx)("h2",{className:"font-semibold text-green-800 mb-2",children:"Funzionalit\xe0 Implementate"}),(0,r.jsxs)("div",{className:"grid md:grid-cols-2 gap-4 text-sm text-green-700",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-medium mb-1",children:"Modalit\xe0 Aggiungi Metri:"}),(0,r.jsxs)("ul",{className:"space-y-1",children:[(0,r.jsx)("li",{children:"✅ Validazione input metri"}),(0,r.jsx)("li",{children:"✅ Selezione bobina compatibile/incompatibile"}),(0,r.jsx)("li",{children:"✅ Opzione BOBINA VUOTA"}),(0,r.jsx)("li",{children:"✅ Ricerca bobine"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-medium mb-1",children:"Modalit\xe0 Modifica Bobina:"}),(0,r.jsxs)("ul",{className:"space-y-1",children:[(0,r.jsx)("li",{children:"✅ Radio button per opzioni"}),(0,r.jsx)("li",{children:"✅ Selezione condizionale bobina"}),(0,r.jsx)("li",{children:"✅ Modifica metri posati"}),(0,r.jsx)("li",{children:"✅ Annulla posa"})]})]})]})]}),(0,r.jsxs)("div",{className:"mt-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:[(0,r.jsx)("h2",{className:"font-semibold text-yellow-800 mb-2",children:"Accessibilit\xe0"}),(0,r.jsxs)("div",{className:"text-sm text-yellow-700 space-y-1",children:[(0,r.jsx)("li",{children:"✅ Attributi ARIA per screen reader"}),(0,r.jsx)("li",{children:"✅ Navigazione da tastiera (Tab, Enter, Escape)"}),(0,r.jsx)("li",{children:"✅ Focus management e indicatori visivi"}),(0,r.jsx)("li",{children:'✅ Messaggi di errore con role="alert"'}),(0,r.jsx)("li",{children:"✅ Etichette descrittive per tutti i controlli"})]})]})]})})})}},94735:e=>{"use strict";e.exports=require("events")},95969:(e,i,t)=>{Promise.resolve().then(t.bind(t,92406))}};var i=require("../../webpack-runtime.js");i.C(e);var t=e=>i(i.s=e),r=i.X(0,[447,991,658,947,203,639,39],()=>t(5529));module.exports=r})();