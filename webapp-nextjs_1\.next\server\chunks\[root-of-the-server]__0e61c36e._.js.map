{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/app/api/responsabili/cantiere/%5BcantiereId%5D/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\n\ninterface Responsabile {\n  id_responsabile: number\n  nome_responsabile: string\n  telefono?: string\n  email?: string\n  experience_level: string\n  id_cantiere: number\n  data_creazione: string\n  attivo: boolean\n}\n\nexport async function GET(\n  request: NextRequest,\n  { params }: { params: { cantiereId: string } }\n) {\n  try {\n    const cantiereId = parseInt(params.cantiereId)\n    \n    if (isNaN(cantiereId)) {\n      return NextResponse.json(\n        { error: 'ID cantiere non valido' },\n        { status: 400 }\n      )\n    }\n\n    // Per ora restituiamo dati mock per testare l'interfaccia\n    // TODO: Implementare chiamata al backend Python\n    const mockResponsabili: Responsabile[] = [\n      {\n        id_responsabile: 1,\n        nome_responsabile: \"<PERSON>\",\n        telefono: \"+39 ************\",\n        email: \"<EMAIL>\",\n        experience_level: \"Senior\",\n        id_cantiere: cantiereId,\n        data_creazione: \"2024-01-15T10:00:00Z\",\n        attivo: true\n      },\n      {\n        id_responsabile: 2,\n        nome_responsabile: \"<PERSON> Verdi\",\n        telefono: \"+39 ************\",\n        email: \"<EMAIL>\",\n        experience_level: \"Senior\",\n        id_cantiere: cantiereId,\n        data_creazione: \"2024-01-20T14:30:00Z\",\n        attivo: true\n      },\n      {\n        id_responsabile: 3,\n        nome_responsabile: \"Anna Bianchi\",\n        telefono: \"+39 ************\",\n        email: \"<EMAIL>\",\n        experience_level: \"Junior\",\n        id_cantiere: cantiereId,\n        data_creazione: \"2024-02-01T09:15:00Z\",\n        attivo: true\n      }\n    ]\n\n    return NextResponse.json({\n      success: true,\n      data: mockResponsabili,\n      total: mockResponsabili.length\n    })\n\n  } catch (error) {\n    console.error('Errore nel recupero responsabili:', error)\n    return NextResponse.json(\n      { error: 'Errore interno del server' },\n      { status: 500 }\n    )\n  }\n}\n\nexport async function POST(\n  request: NextRequest,\n  { params }: { params: { cantiereId: string } }\n) {\n  try {\n    const cantiereId = parseInt(params.cantiereId)\n    const body = await request.json()\n    \n    if (isNaN(cantiereId)) {\n      return NextResponse.json(\n        { error: 'ID cantiere non valido' },\n        { status: 400 }\n      )\n    }\n\n    // Validazione base\n    if (!body.nome_responsabile) {\n      return NextResponse.json(\n        { error: 'Nome responsabile obbligatorio' },\n        { status: 400 }\n      )\n    }\n\n    // Per ora restituiamo un mock del responsabile creato\n    // TODO: Implementare chiamata al backend Python\n    const nuovoResponsabile: Responsabile = {\n      id_responsabile: Math.floor(Math.random() * 1000) + 100,\n      nome_responsabile: body.nome_responsabile,\n      telefono: body.telefono || null,\n      email: body.email || null,\n      experience_level: body.experience_level || 'Senior',\n      id_cantiere: cantiereId,\n      data_creazione: new Date().toISOString(),\n      attivo: true\n    }\n\n    return NextResponse.json({\n      success: true,\n      data: nuovoResponsabile\n    }, { status: 201 })\n\n  } catch (error) {\n    console.error('Errore nella creazione responsabile:', error)\n    return NextResponse.json(\n      { error: 'Errore interno del server' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAaO,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAAsC;IAE9C,IAAI;QACF,MAAM,aAAa,SAAS,OAAO,UAAU;QAE7C,IAAI,MAAM,aAAa;YACrB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAyB,GAClC;gBAAE,QAAQ;YAAI;QAElB;QAEA,0DAA0D;QAC1D,gDAAgD;QAChD,MAAM,mBAAmC;YACvC;gBACE,iBAAiB;gBACjB,mBAAmB;gBACnB,UAAU;gBACV,OAAO;gBACP,kBAAkB;gBAClB,aAAa;gBACb,gBAAgB;gBAChB,QAAQ;YACV;YACA;gBACE,iBAAiB;gBACjB,mBAAmB;gBACnB,UAAU;gBACV,OAAO;gBACP,kBAAkB;gBAClB,aAAa;gBACb,gBAAgB;gBAChB,QAAQ;YACV;YACA;gBACE,iBAAiB;gBACjB,mBAAmB;gBACnB,UAAU;gBACV,OAAO;gBACP,kBAAkB;gBAClB,aAAa;gBACb,gBAAgB;gBAChB,QAAQ;YACV;SACD;QAED,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;YACN,OAAO,iBAAiB,MAAM;QAChC;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA4B,GACrC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,KACpB,OAAoB,EACpB,EAAE,MAAM,EAAsC;IAE9C,IAAI;QACF,MAAM,aAAa,SAAS,OAAO,UAAU;QAC7C,MAAM,OAAO,MAAM,QAAQ,IAAI;QAE/B,IAAI,MAAM,aAAa;YACrB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAyB,GAClC;gBAAE,QAAQ;YAAI;QAElB;QAEA,mBAAmB;QACnB,IAAI,CAAC,KAAK,iBAAiB,EAAE;YAC3B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAiC,GAC1C;gBAAE,QAAQ;YAAI;QAElB;QAEA,sDAAsD;QACtD,gDAAgD;QAChD,MAAM,oBAAkC;YACtC,iBAAiB,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,QAAQ;YACpD,mBAAmB,KAAK,iBAAiB;YACzC,UAAU,KAAK,QAAQ,IAAI;YAC3B,OAAO,KAAK,KAAK,IAAI;YACrB,kBAAkB,KAAK,gBAAgB,IAAI;YAC3C,aAAa;YACb,gBAAgB,IAAI,OAAO,WAAW;YACtC,QAAQ;QACV;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;QACR,GAAG;YAAE,QAAQ;QAAI;IAEnB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wCAAwC;QACtD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA4B,GACrC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}