{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|public).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "bF7oDIAQsiDlYBqvLR/WiZdX1cn2FuoutsR3XKrSPcA=", "__NEXT_PREVIEW_MODE_ID": "6f5ef04be28e50c0c793d9c51a66bd7d", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "69ba42b9bf969123251bf14ff53afe374e0ff3951f40b7eb5cad16ea24ee5aef", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "b99da583455ad601e2f37d868c0393271922ca2b93c0b8455fcf31e588ae1a4d"}}}, "sortedMiddleware": ["/"], "functions": {}}