{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAuth } from '@/contexts/AuthContext'\n\nexport default function Home() {\n  const { user, cantiere, isAuthenticated, isLoading } = useAuth()\n  const router = useRouter()\n\n  useEffect(() => {\n    console.log('🏠 HomePage: Stato corrente:', {\n      isLoading,\n      isAuthenticated,\n      user: user ? { id: user.id_utente, ruolo: user.ruolo } : null,\n      cantiere: cantiere ? { id: cantiere.id_cantiere } : null\n    })\n\n    // ASPETTA che il caricamento sia completato\n    if (isLoading) {\n      console.log('🏠 HomePage: Ancora in caricamento, attendo...')\n      return\n    }\n\n    // Se NON autenticato, reindirizza al login\n    if (!isAuthenticated) {\n      console.log('🏠 HomePage: Utente NON autenticato, reindirizzamento a /login')\n      router.replace('/login')\n      return\n    }\n\n    // Se autenticato, reindirizza in base al ruolo SOLO se abbiamo dati validi\n    if (isAuthenticated && user) {\n      console.log('🏠 HomePage: Utente autenticato, reindirizzamento in base al ruolo:', user.ruolo)\n\n      if (user.ruolo === 'owner') {\n        console.log('🏠 HomePage: Reindirizzamento admin a /admin')\n        router.replace('/admin')\n      } else if (user.ruolo === 'user') {\n        console.log('🏠 HomePage: Reindirizzamento utente standard a /cantieri')\n        router.replace('/cantieri')\n      } else if (user.ruolo === 'cantieri_user') {\n        console.log('🏠 HomePage: Reindirizzamento utente cantiere a /cavi')\n        router.replace('/cavi')\n      } else {\n        console.log('🏠 HomePage: Ruolo sconosciuto, reindirizzamento a /login')\n        router.replace('/login')\n      }\n    } else if (isAuthenticated && cantiere && !user) {\n      // Login cantiere senza utente\n      console.log('🏠 HomePage: Login cantiere rilevato, reindirizzamento a /cavi')\n      router.replace('/cavi')\n    } else if (isAuthenticated && !user && !cantiere) {\n      // Stato inconsistente - autenticato ma senza dati\n      console.error('🏠 HomePage: Stato inconsistente - autenticato ma senza user/cantiere')\n      router.replace('/login')\n    }\n  }, [isAuthenticated, isLoading, user, cantiere, router])\n\n  // Mostra un indicatore di caricamento durante il reindirizzamento\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 flex items-center justify-center\">\n      <div className=\"text-center\">\n        <h1 className=\"text-3xl font-bold text-slate-900 mb-4\">\n          CABLYS\n        </h1>\n        <p className=\"text-slate-600 mb-6\">\n          {isLoading ? 'Verifica autenticazione...' : 'Reindirizzamento in corso...'}\n        </p>\n        <div className=\"w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto\"></div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,eAAe,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC7D,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,QAAQ,GAAG,CAAC,gCAAgC;gBAC1C;gBACA;gBACA,MAAM,OAAO;oBAAE,IAAI,KAAK,SAAS;oBAAE,OAAO,KAAK,KAAK;gBAAC,IAAI;gBACzD,UAAU,WAAW;oBAAE,IAAI,SAAS,WAAW;gBAAC,IAAI;YACtD;YAEA,4CAA4C;YAC5C,IAAI,WAAW;gBACb,QAAQ,GAAG,CAAC;gBACZ;YACF;YAEA,2CAA2C;YAC3C,IAAI,CAAC,iBAAiB;gBACpB,QAAQ,GAAG,CAAC;gBACZ,OAAO,OAAO,CAAC;gBACf;YACF;YAEA,2EAA2E;YAC3E,IAAI,mBAAmB,MAAM;gBAC3B,QAAQ,GAAG,CAAC,uEAAuE,KAAK,KAAK;gBAE7F,IAAI,KAAK,KAAK,KAAK,SAAS;oBAC1B,QAAQ,GAAG,CAAC;oBACZ,OAAO,OAAO,CAAC;gBACjB,OAAO,IAAI,KAAK,KAAK,KAAK,QAAQ;oBAChC,QAAQ,GAAG,CAAC;oBACZ,OAAO,OAAO,CAAC;gBACjB,OAAO,IAAI,KAAK,KAAK,KAAK,iBAAiB;oBACzC,QAAQ,GAAG,CAAC;oBACZ,OAAO,OAAO,CAAC;gBACjB,OAAO;oBACL,QAAQ,GAAG,CAAC;oBACZ,OAAO,OAAO,CAAC;gBACjB;YACF,OAAO,IAAI,mBAAmB,YAAY,CAAC,MAAM;gBAC/C,8BAA8B;gBAC9B,QAAQ,GAAG,CAAC;gBACZ,OAAO,OAAO,CAAC;YACjB,OAAO,IAAI,mBAAmB,CAAC,QAAQ,CAAC,UAAU;gBAChD,kDAAkD;gBAClD,QAAQ,KAAK,CAAC;gBACd,OAAO,OAAO,CAAC;YACjB;QACF;yBAAG;QAAC;QAAiB;QAAW;QAAM;QAAU;KAAO;IAEvD,kEAAkE;IAClE,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAAyC;;;;;;8BAGvD,6LAAC;oBAAE,WAAU;8BACV,YAAY,+BAA+B;;;;;;8BAE9C,6LAAC;oBAAI,WAAU;;;;;;;;;;;;;;;;;AAIvB;GAnEwB;;QACiC,kIAAA,CAAA,UAAO;QAC/C,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}]}