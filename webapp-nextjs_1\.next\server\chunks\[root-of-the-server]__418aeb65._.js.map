{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/app/api/cavi/%5BcantiereId%5D/%5BcavoId%5D/collegamento/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\n\ninterface CollegamentoRequest {\n  lato: 'partenza' | 'arrivo'\n  responsabile?: string\n}\n\ninterface ScollegamentoRequest {\n  lato?: 'partenza' | 'arrivo'\n}\n\n// POST - Collega un lato del cavo\nexport async function POST(\n  request: NextRequest,\n  { params }: { params: { cantiereId: string; cavoId: string } }\n) {\n  try {\n    const { cantiereId, cavoId } = params\n    const body: CollegamentoRequest = await request.json()\n\n    // Validazione parametri\n    if (!cantiereId || !cavoId) {\n      return NextResponse.json(\n        { error: 'Parametri cantiere ID e cavo ID richiesti' },\n        { status: 400 }\n      )\n    }\n\n    if (!body.lato || !['partenza', 'arrivo'].includes(body.lato)) {\n      return NextResponse.json(\n        { error: 'Lato richiesto: \"partenza\" o \"arrivo\"' },\n        { status: 400 }\n      )\n    }\n\n    // Ottieni il token di autenticazione dalla richiesta\n    const authHeader = request.headers.get('authorization')\n    if (!authHeader) {\n      return NextResponse.json(\n        { error: 'Token di autenticazione richiesto' },\n        { status: 401 }\n      )\n    }\n\n    // Chiama l'API backend Python\n    const backendUrl = process.env.BACKEND_URL || 'http://localhost:8000'\n    const response = await fetch(\n      `${backendUrl}/cavi/${cantiereId}/${cavoId}/collegamento`,\n      {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': authHeader,\n        },\n        body: JSON.stringify({\n          lato: body.lato,\n          responsabile: body.responsabile || 'cantiere'\n        }),\n      }\n    )\n\n    if (!response.ok) {\n      const errorData = await response.json().catch(() => ({}))\n      return NextResponse.json(\n        { \n          error: errorData.detail || `Errore backend: ${response.status}`,\n          detail: errorData.detail \n        },\n        { status: response.status }\n      )\n    }\n\n    const data = await response.json()\n    return NextResponse.json(data)\n\n  } catch (error: any) {\n    console.error('Errore nel collegamento cavo:', error)\n    return NextResponse.json(\n      { error: 'Errore interno del server', detail: error.message },\n      { status: 500 }\n    )\n  }\n}\n\n// DELETE - Scollega un lato del cavo\nexport async function DELETE(\n  request: NextRequest,\n  { params }: { params: { cantiereId: string; cavoId: string } }\n) {\n  try {\n    const { cantiereId, cavoId } = params\n    \n    // Leggi il body per ottenere il lato da scollegare\n    let body: ScollegamentoRequest = {}\n    try {\n      body = await request.json()\n    } catch {\n      // Se non c'è body, scollega entrambi i lati\n      body = {}\n    }\n\n    // Validazione parametri\n    if (!cantiereId || !cavoId) {\n      return NextResponse.json(\n        { error: 'Parametri cantiere ID e cavo ID richiesti' },\n        { status: 400 }\n      )\n    }\n\n    // Ottieni il token di autenticazione dalla richiesta\n    const authHeader = request.headers.get('authorization')\n    if (!authHeader) {\n      return NextResponse.json(\n        { error: 'Token di autenticazione richiesto' },\n        { status: 401 }\n      )\n    }\n\n    // Chiama l'API backend Python\n    const backendUrl = process.env.BACKEND_URL || 'http://localhost:8000'\n\n    // Il backend Python richiede il lato nel path URL\n    // Se non è specificato un lato, scolleghiamo entrambi i lati\n    if (body.lato) {\n      // Scollega un lato specifico\n      const url = `${backendUrl}/cavi/${cantiereId}/${cavoId}/collegamento/${body.lato}`\n      const response = await fetch(url, {\n        method: 'DELETE',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': authHeader,\n        },\n      })\n\n      if (!response.ok) {\n        const errorData = await response.json().catch(() => ({}))\n        return NextResponse.json(\n          {\n            error: errorData.detail || `Errore backend: ${response.status}`,\n            detail: errorData.detail\n          },\n          { status: response.status }\n        )\n      }\n\n      const data = await response.json()\n      return NextResponse.json(data)\n    } else {\n      // Scollega entrambi i lati (prima partenza, poi arrivo)\n      let finalData = null\n\n      // Scollega partenza\n      try {\n        const urlPartenza = `${backendUrl}/cavi/${cantiereId}/${cavoId}/collegamento/partenza`\n        const responsePartenza = await fetch(urlPartenza, {\n          method: 'DELETE',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': authHeader,\n          },\n        })\n        if (responsePartenza.ok) {\n          finalData = await responsePartenza.json()\n        }\n      } catch (error) {\n        // Ignora errori se il lato partenza non è collegato\n      }\n\n      // Scollega arrivo\n      try {\n        const urlArrivo = `${backendUrl}/cavi/${cantiereId}/${cavoId}/collegamento/arrivo`\n        const responseArrivo = await fetch(urlArrivo, {\n          method: 'DELETE',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': authHeader,\n          },\n        })\n        if (responseArrivo.ok) {\n          finalData = await responseArrivo.json()\n        }\n      } catch (error) {\n        // Ignora errori se il lato arrivo non è collegato\n      }\n\n      if (finalData) {\n        return NextResponse.json(finalData)\n      } else {\n        return NextResponse.json(\n          { error: 'Nessun collegamento da scollegare' },\n          { status: 400 }\n        )\n      }\n    }\n\n  } catch (error: any) {\n    console.error('Errore nello scollegamento cavo:', error)\n    return NextResponse.json(\n      { error: 'Errore interno del server', detail: error.message },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAYO,eAAe,KACpB,OAAoB,EACpB,EAAE,MAAM,EAAsD;IAE9D,IAAI;QACF,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,GAAG;QAC/B,MAAM,OAA4B,MAAM,QAAQ,IAAI;QAEpD,wBAAwB;QACxB,IAAI,CAAC,cAAc,CAAC,QAAQ;YAC1B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA4C,GACrD;gBAAE,QAAQ;YAAI;QAElB;QAEA,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC;YAAC;YAAY;SAAS,CAAC,QAAQ,CAAC,KAAK,IAAI,GAAG;YAC7D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAwC,GACjD;gBAAE,QAAQ;YAAI;QAElB;QAEA,qDAAqD;QACrD,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;QACvC,IAAI,CAAC,YAAY;YACf,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAoC,GAC7C;gBAAE,QAAQ;YAAI;QAElB;QAEA,8BAA8B;QAC9B,MAAM,aAAa,QAAQ,GAAG,CAAC,WAAW,IAAI;QAC9C,MAAM,WAAW,MAAM,MACrB,GAAG,WAAW,MAAM,EAAE,WAAW,CAAC,EAAE,OAAO,aAAa,CAAC,EACzD;YACE,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,iBAAiB;YACnB;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB,MAAM,KAAK,IAAI;gBACf,cAAc,KAAK,YAAY,IAAI;YACrC;QACF;QAGF,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;YACvD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,OAAO,UAAU,MAAM,IAAI,CAAC,gBAAgB,EAAE,SAAS,MAAM,EAAE;gBAC/D,QAAQ,UAAU,MAAM;YAC1B,GACA;gBAAE,QAAQ,SAAS,MAAM;YAAC;QAE9B;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAE3B,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;YAA6B,QAAQ,MAAM,OAAO;QAAC,GAC5D;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,OACpB,OAAoB,EACpB,EAAE,MAAM,EAAsD;IAE9D,IAAI;QACF,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,GAAG;QAE/B,mDAAmD;QACnD,IAAI,OAA6B,CAAC;QAClC,IAAI;YACF,OAAO,MAAM,QAAQ,IAAI;QAC3B,EAAE,OAAM;YACN,4CAA4C;YAC5C,OAAO,CAAC;QACV;QAEA,wBAAwB;QACxB,IAAI,CAAC,cAAc,CAAC,QAAQ;YAC1B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA4C,GACrD;gBAAE,QAAQ;YAAI;QAElB;QAEA,qDAAqD;QACrD,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;QACvC,IAAI,CAAC,YAAY;YACf,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAoC,GAC7C;gBAAE,QAAQ;YAAI;QAElB;QAEA,8BAA8B;QAC9B,MAAM,aAAa,QAAQ,GAAG,CAAC,WAAW,IAAI;QAE9C,kDAAkD;QAClD,6DAA6D;QAC7D,IAAI,KAAK,IAAI,EAAE;YACb,6BAA6B;YAC7B,MAAM,MAAM,GAAG,WAAW,MAAM,EAAE,WAAW,CAAC,EAAE,OAAO,cAAc,EAAE,KAAK,IAAI,EAAE;YAClF,MAAM,WAAW,MAAM,MAAM,KAAK;gBAChC,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,iBAAiB;gBACnB;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;gBACvD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBACE,OAAO,UAAU,MAAM,IAAI,CAAC,gBAAgB,EAAE,SAAS,MAAM,EAAE;oBAC/D,QAAQ,UAAU,MAAM;gBAC1B,GACA;oBAAE,QAAQ,SAAS,MAAM;gBAAC;YAE9B;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QAC3B,OAAO;YACL,wDAAwD;YACxD,IAAI,YAAY;YAEhB,oBAAoB;YACpB,IAAI;gBACF,MAAM,cAAc,GAAG,WAAW,MAAM,EAAE,WAAW,CAAC,EAAE,OAAO,sBAAsB,CAAC;gBACtF,MAAM,mBAAmB,MAAM,MAAM,aAAa;oBAChD,QAAQ;oBACR,SAAS;wBACP,gBAAgB;wBAChB,iBAAiB;oBACnB;gBACF;gBACA,IAAI,iBAAiB,EAAE,EAAE;oBACvB,YAAY,MAAM,iBAAiB,IAAI;gBACzC;YACF,EAAE,OAAO,OAAO;YACd,oDAAoD;YACtD;YAEA,kBAAkB;YAClB,IAAI;gBACF,MAAM,YAAY,GAAG,WAAW,MAAM,EAAE,WAAW,CAAC,EAAE,OAAO,oBAAoB,CAAC;gBAClF,MAAM,iBAAiB,MAAM,MAAM,WAAW;oBAC5C,QAAQ;oBACR,SAAS;wBACP,gBAAgB;wBAChB,iBAAiB;oBACnB;gBACF;gBACA,IAAI,eAAe,EAAE,EAAE;oBACrB,YAAY,MAAM,eAAe,IAAI;gBACvC;YACF,EAAE,OAAO,OAAO;YACd,kDAAkD;YACpD;YAEA,IAAI,WAAW;gBACb,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAC3B,OAAO;gBACL,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,OAAO;gBAAoC,GAC7C;oBAAE,QAAQ;gBAAI;YAElB;QACF;IAEF,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,oCAAoC;QAClD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;YAA6B,QAAQ,MAAM,OAAO;QAAC,GAC5D;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}